/**
 * 远程控制命令系统
 * 基于EventBus实现可扩展的控制命令处理
 */

/**
 * 命令处理器注册表
 */
const commandHandlers = new Map()

/**
 * 命令执行历史
 */
const commandHistory = []

/**
 * 最大历史记录数
 */
const MAX_HISTORY = 100

/**
 * 注册命令处理器
 * @param {string} command 命令名称
 * @param {function} handler 处理函数
 * @param {object} options 选项
 */
export function registerCommandHandler(command, handler, options = {}) {
    if (!commandHandlers.has(command)) {
        commandHandlers.set(command, [])
    }
    
    commandHandlers.get(command).push({
        handler,
        priority: options.priority || 0,
        description: options.description || '',
        category: options.category || 'default'
    })
    
    // 按优先级排序
    commandHandlers.get(command).sort((a, b) => b.priority - a.priority)
    
    console.log(`注册远程控制命令处理器: ${command}`)
}

/**
 * 移除命令处理器
 * @param {string} command 命令名称
 * @param {function} handler 处理函数
 */
export function unregisterCommandHandler(command, handler) {
    if (commandHandlers.has(command)) {
        const handlers = commandHandlers.get(command)
        const index = handlers.findIndex(item => item.handler === handler)
        if (index > -1) {
            handlers.splice(index, 1)
            console.log(`移除远程控制命令处理器: ${command}`)
        }
    }
}

/**
 * 执行命令
 * @param {string} command 命令名称
 * @param {object} data 命令数据
 * @param {object} context 执行上下文
 */
export function executeCommand(command, data = {}, context = {}) {
    console.log(`执行远程控制命令: ${command}`, data)
    
    // 记录命令历史
    addToHistory(command, data, context)
    
    if (!commandHandlers.has(command)) {
        console.warn(`未找到命令处理器: ${command}`)
        return false
    }
    
    const handlers = commandHandlers.get(command)
    let executed = false
    
    for (const handlerInfo of handlers) {
        try {
            const result = handlerInfo.handler(data, context)
            if (result !== false) {
                executed = true
                // 如果处理器返回true，停止执行后续处理器
                if (result === true) {
                    break
                }
            }
        } catch (error) {
            console.error(`命令处理器执行失败 [${command}]:`, error)
        }
    }
    
    return executed
}

/**
 * 添加到历史记录
 */
function addToHistory(command, data, context) {
    commandHistory.unshift({
        command,
        data,
        context,
        timestamp: Date.now(),
        id: generateId()
    })
    
    // 限制历史记录数量
    if (commandHistory.length > MAX_HISTORY) {
        commandHistory.splice(MAX_HISTORY)
    }
}

/**
 * 获取命令历史
 */
export function getCommandHistory(limit = 20) {
    return commandHistory.slice(0, limit)
}

/**
 * 清空命令历史
 */
export function clearCommandHistory() {
    commandHistory.length = 0
}

/**
 * 获取所有注册的命令
 */
export function getRegisteredCommands() {
    const commands = {}
    for (const [command, handlers] of commandHandlers) {
        commands[command] = handlers.map(h => ({
            description: h.description,
            category: h.category,
            priority: h.priority
        }))
    }
    return commands
}

/**
 * 生成唯一ID
 */
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 初始化基础命令处理器
 */
export function initializeBasicCommands() {
    // 地图放大命令
    registerCommandHandler('ZOOM_IN', (data, context) => {
        if (window.FreeEarth && window.FreeEarth.navigator) {
            window.FreeEarth.navigator.zoomInStart()
            setTimeout(() => {
                window.FreeEarth.navigator.zoomInEnd()
            }, data.duration || 500)
            return true
        }
        return false
    }, {
        description: '放大地图',
        category: 'map',
        priority: 10
    })
    
    // 地图缩小命令
    registerCommandHandler('ZOOM_OUT', (data, context) => {
        if (window.FreeEarth && window.FreeEarth.navigator) {
            window.FreeEarth.navigator.zoomOutStart()
            setTimeout(() => {
                window.FreeEarth.navigator.zoomOutEnd()
            }, data.duration || 500)
            return true
        }
        return false
    }, {
        description: '缩小地图',
        category: 'map',
        priority: 10
    })
    
    // 复位视角命令
    registerCommandHandler('RESET_VIEW', (data, context) => {
        if (window.FreeEarth && window.FreeEarth.navigator) {
            window.FreeEarth.navigator.resetView()
            return true
        }
        return false
    }, {
        description: '复位视角',
        category: 'map',
        priority: 10
    })
    
    // 指北针命令
    registerCommandHandler('RESET_NORTH', (data, context) => {
        if (window.FreeEarth && window.FreeEarth.navigator) {
            window.FreeEarth.navigator.resetCameraRotation()
            return true
        }
        return false
    }, {
        description: '指北针复位',
        category: 'map',
        priority: 10
    })
    
    // 切换地图模式命令
    registerCommandHandler('CHANGE_MAP_MODE', (data, context) => {
        const { mode } = data
        if (mode && context.eventBus) {
            context.eventBus.$emit('changeMapMode', mode)
            return true
        }
        return false
    }, {
        description: '切换地图模式',
        category: 'map',
        priority: 10
    })
    
    // 飞行到指定位置命令
    registerCommandHandler('FLY_TO', (data, context) => {
        const { destination, rotation, duration } = data
        if (destination && window.FreeEarth) {
            const options = {
                destination: destination,
                rotation: rotation || [0.0, -90.0, 0.0],
                duration: duration || 3.0
            }
            window.FreeEarth.flyToDest(options)
            return true
        }
        return false
    }, {
        description: '飞行到指定位置',
        category: 'map',
        priority: 10
    })
    
    // 显示/隐藏目标命令
    registerCommandHandler('TOGGLE_TARGETS', (data, context) => {
        const { show } = data
        if (context.eventBus) {
            if (show) {
                context.eventBus.$emit('showAllTarget')
            } else {
                context.eventBus.$emit('hideAllTarget')
            }
            return true
        }
        return false
    }, {
        description: '显示/隐藏目标',
        category: 'display',
        priority: 10
    })
    
    // 显示/隐藏全景命令
    registerCommandHandler('TOGGLE_PANORAMA', (data, context) => {
        const { show } = data
        if (context.eventBus) {
            if (show) {
                context.eventBus.$emit('showAllPanorama')
            } else {
                context.eventBus.$emit('hideAllPanorama')
            }
            return true
        }
        return false
    }, {
        description: '显示/隐藏全景',
        category: 'display',
        priority: 10
    })
    
    console.log('基础远程控制命令已初始化')
}

/**
 * 远程控制命令管理器
 */
export class RemoteControlCommandManager {
    constructor(eventBus) {
        this.eventBus = eventBus
        this.isInitialized = false
        this.init()
    }
    
    init() {
        if (this.isInitialized) {
            return
        }
        
        // 初始化基础命令
        initializeBasicCommands()
        
        // 监听远程控制命令事件
        this.eventBus.$on('remote-control-command', this.handleRemoteCommand.bind(this))
        
        this.isInitialized = true
        console.log('远程控制命令管理器已初始化')
    }
    
    handleRemoteCommand(commandData) {
        const { command, data = {} } = commandData
        const context = {
            eventBus: this.eventBus,
            timestamp: Date.now()
        }
        
        executeCommand(command, data, context)
    }
    
    destroy() {
        if (this.eventBus) {
            this.eventBus.$off('remote-control-command', this.handleRemoteCommand)
        }
        this.isInitialized = false
    }
}

export default {
    registerCommandHandler,
    unregisterCommandHandler,
    executeCommand,
    getCommandHistory,
    clearCommandHistory,
    getRegisteredCommands,
    initializeBasicCommands,
    RemoteControlCommandManager
}
