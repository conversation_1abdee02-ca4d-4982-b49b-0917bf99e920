/**
 * 模拟的远程控制WebSocket服务器
 * 用于测试远程控制功能
 */

const WebSocket = require('ws');
const http = require('http');

// 创建HTTP服务器
const server = http.createServer();

// 创建WebSocket服务器
const wss = new WebSocket.Server({
    server,
    path: '/gis-services/remote-control'
});

// 存储用户会话
const userSessions = new Map();
// 存储控制会话
const controlSessions = new Map();

// 生成6位随机控制码
function generateControlCode() {
    return Math.floor(100000 + Math.random() * 900000).toString();
}

// 创建标准消息格式
function createMessage(type, message, data = null) {
    return JSON.stringify({
        type,
        message,
        timestamp: Date.now(),
        data
    });
}

// WebSocket连接处理
wss.on('connection', (ws, req) => {
    // 从URL中提取用户ID
    const urlParts = req.url.split('/');
    const userId = urlParts[urlParts.length - 1];

    console.log(`用户 ${userId} 连接到WebSocket`);

    // 存储用户会话
    userSessions.set(userId, ws);
    ws.userId = userId;

    // 发送连接成功消息
    ws.send(createMessage('CONNECT_SUCCESS', '连接成功'));

    // 消息处理
    ws.on('message', (data) => {
        try {
            const message = JSON.parse(data);
            console.log(`收到来自用户 ${userId} 的消息:`, message);

            switch (message.type) {
                case 'REQUEST_CONTROL_CODE':
                    handleRequestControlCode(ws, userId);
                    break;

                case 'CONNECT_WITH_CODE':
                    handleConnectWithCode(ws, userId, message.controlCode);
                    break;

                case 'DISCONNECT':
                    handleDisconnect(ws, userId);
                    break;

                case 'CONTROL_COMMAND':
                    handleControlCommand(ws, userId, message);
                    break;

                default:
                    console.log('未知的消息类型:', message.type);
            }
        } catch (error) {
            console.error('解析消息失败:', error);
            ws.send(createMessage('ERROR', '消息格式错误'));
        }
    });

    // 连接关闭处理
    ws.on('close', () => {
        console.log(`用户 ${userId} 断开连接`);
        userSessions.delete(userId);

        // 清理相关的控制会话
        for (const [controlCode, session] of controlSessions) {
            if (session.controlledUserId === userId || session.controllerUserId === userId) {
                controlSessions.delete(controlCode);

                // 通知对方断开连接
                const otherUserId = session.controlledUserId === userId ?
                    session.controllerUserId : session.controlledUserId;

                if (otherUserId) {
                    const otherWs = userSessions.get(otherUserId);
                    if (otherWs) {
                        otherWs.send(createMessage('CONTROL_DISCONNECTED', '远程控制已断开'));
                    }
                }
                break;
            }
        }
    });

    // 错误处理
    ws.on('error', (error) => {
        console.error(`用户 ${userId} WebSocket错误:`, error);
    });
});

// 处理请求控制码
function handleRequestControlCode(ws, userId) {
    const controlCode = generateControlCode();

    // 创建控制会话
    const session = {
        controlCode,
        controlledUserId: userId,
        controllerUserId: null,
        status: 'WAITING',
        createTime: Date.now(),
        expireTime: Date.now() + 10 * 60 * 1000 // 10分钟过期
    };

    controlSessions.set(controlCode, session);

    console.log(`为用户 ${userId} 生成控制码: ${controlCode}`);

    ws.send(createMessage('CONTROL_CODE_GENERATED', '控制码生成成功', {
        controlCode
    }));
}

// 处理通过控制码连接
function handleConnectWithCode(ws, userId, controlCode) {
    const session = controlSessions.get(controlCode);

    if (!session) {
        ws.send(createMessage('ERROR', '控制码不存在'));
        return;
    }

    if (Date.now() > session.expireTime) {
        controlSessions.delete(controlCode);
        ws.send(createMessage('ERROR', '控制码已过期'));
        return;
    }

    if (session.status === 'CONNECTED') {
        ws.send(createMessage('ERROR', '控制码已被其他用户连接'));
        return;
    }

    // 建立连接
    session.controllerUserId = userId;
    session.status = 'CONNECTED';
    session.connectTime = Date.now();

    console.log(`用户 ${userId} 通过控制码 ${controlCode} 连接到用户 ${session.controlledUserId}`);

    // 通知控制端连接成功
    ws.send(createMessage('CONTROL_CONNECTED', '远程控制连接成功', {
        controlCode,
        controlledUserId: session.controlledUserId
    }));

    // 通知被控制端
    const controlledWs = userSessions.get(session.controlledUserId);
    if (controlledWs) {
        controlledWs.send(createMessage('CONTROLLER_CONNECTED', '控制端已连接', {
            controllerUserId: userId,
            controlCode
        }));
    }
}

// 处理断开连接
function handleDisconnect(ws, userId) {
    for (const [controlCode, session] of controlSessions) {
        if (session.controlledUserId === userId || session.controllerUserId === userId) {
            session.status = 'DISCONNECTED';
            session.disconnectTime = Date.now();

            console.log(`控制码 ${controlCode} 的连接已断开`);

            // 通知对方断开连接
            const otherUserId = session.controlledUserId === userId ?
                session.controllerUserId : session.controlledUserId;

            if (otherUserId) {
                const otherWs = userSessions.get(otherUserId);
                if (otherWs) {
                    otherWs.send(createMessage('CONTROL_DISCONNECTED', '远程控制已断开'));
                }
            }

            ws.send(createMessage('CONTROL_DISCONNECTED', '远程控制已断开'));

            // 延迟清理会话
            setTimeout(() => {
                controlSessions.delete(controlCode);
            }, 5000);

            break;
        }
    }
}

// 处理控制命令
function handleControlCommand(ws, userId, message) {
    for (const [controlCode, session] of controlSessions) {
        if (session.controllerUserId === userId && session.status === 'CONNECTED') {
            // 转发命令到被控制端
            const controlledWs = userSessions.get(session.controlledUserId);
            if (controlledWs) {
                controlledWs.send(JSON.stringify(message));
                console.log(`转发控制命令 ${message.data.command} 从 ${userId} 到 ${session.controlledUserId}`);
            } else {
                ws.send(createMessage('ERROR', '被控制端已断开连接'));
            }
            return;
        }
    }

    ws.send(createMessage('ERROR', '未建立远程控制连接'));
}

// 定期清理过期会话
setInterval(() => {
    const now = Date.now();
    for (const [controlCode, session] of controlSessions) {
        if (now > session.expireTime) {
            console.log(`清理过期会话: ${controlCode}`);
            controlSessions.delete(controlCode);
        }
    }
}, 60000); // 每分钟清理一次

// 启动服务器
const PORT = 8092;
server.listen(PORT, () => {
    console.log(`模拟WebSocket服务器启动在端口 ${PORT}`);
    console.log(`WebSocket端点: ws://localhost:${PORT}/gis-services/remote-control/{userId}`);
    console.log('当前连接的用户:', Array.from(userSessions.keys()));
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n正在关闭服务器...');
    wss.close(() => {
        server.close(() => {
            console.log('服务器已关闭');
            process.exit(0);
        });
    });
});

module.exports = { server, wss };
