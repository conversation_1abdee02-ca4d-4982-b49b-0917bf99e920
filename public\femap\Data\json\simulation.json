{"startTime": "2025/03/13 07:00:00", "endTime": "2025/03/14 07:00:00", "sceneCommands": [{"field": "FeMap", "command": "setDepthTest", "parameters": false}, {"field": "FeWeatherManage", "command": "setLightingVisible", "parameters": false}, {"field": "FeWeatherManage", "command": "setCloundVisible", "parameters": false}, {"field": "SimulationManager", "command": "", "parameters": [{"type": "Wall", "options": {"id": "红方导弹阵地", "center": [117.84681167934484, 25.686747190702327], "radius": 5000, "height": 1000, "fillElevation": [0, 0.3], "color": ["rgb(255, 0, 0)", "rgb(255, 0, 0)"], "alpha": 0.5, "show": true, "fill": true}}, {"type": "Wall", "options": {"id": "红方坦克阵地", "center": [117.02279931670907, 25.086637134497185], "radius": 5000, "height": 1000, "fillElevation": [0, 0.3], "color": ["rgb(255, 0, 0)", "rgb(255, 0, 0)"], "alpha": 0.5, "show": true, "fill": true}}, {"type": "Model", "options": {"id": "红方坦克0", "position": [117.02279931670907, 25.086637134497185, 0], "objectType": "land", "url": "static/models/glb/T99.glb", "image": "static/img/entity/雷达车-红.png", "scale": 100.0, "imageScale": 0.15, "billBoardShow": true, "imageOffsetHeading": -90, "effectList": [{"id": "effect_dynnamicLineEffect_0", "type": "dynnamicLineEffect", "destID": "红方地面站0", "positions": [[102, 39, 10000], [105, 39, 10000], [105, 45, 10000]], "color": "rgb(237,102,97)", "alpha": 0.8}, {"id": "effect_dynnamicLineEffect_1", "type": "dynnamicLineEffect", "destID": "红方地面站1", "positions": [[102, 39, 10000], [105, 39, 10000], [105, 45, 10000]], "color": "rgb(237,102,97)", "alpha": 0.8}, {"id": "effect_dynnamicLineEffect_2", "type": "dynnamicLineEffect", "destID": "红方地面站2", "positions": [[102, 39, 10000], [105, 39, 10000], [105, 45, 10000]], "color": "rgb(237,102,97)", "alpha": 0.8}]}}, {"type": "Model", "options": {"id": "红方坦克1", "position": [117.02279931670907, 25.068637, 0], "objectType": "land", "url": "static/models/glb/T99.glb", "image": "static/img/entity/雷达车-红.png", "scale": 100.0, "imageScale": 0.15, "imageOffsetHeading": -90, "billBoardShow": true}}, {"type": "Model", "options": {"id": "红方坦克2", "position": [117.02279931670907, 25.104637, 0], "objectType": "land", "url": "static/models/glb/T99.glb", "image": "static/img/entity/雷达车-红.png", "scale": 100.0, "imageScale": 0.15, "imageOffsetHeading": -90, "billBoardShow": true}}, {"type": "Model", "options": {"id": "红方坦克3", "position": [117.004799, 25.086637134497185, 0], "objectType": "land", "url": "static/models/glb/T99.glb", "image": "static/img/entity/雷达车-红.png", "scale": 100.0, "imageScale": 0.15, "imageOffsetHeading": -90, "billBoardShow": true}}, {"type": "Model", "options": {"id": "红方坦克4", "position": [117.004799, 25.068637, 0], "objectType": "land", "url": "static/models/glb/T99.glb", "image": "static/img/entity/雷达车-红.png", "scale": 100.0, "imageScale": 0.15, "imageOffsetHeading": -90, "billBoardShow": true}}, {"type": "Model", "options": {"id": "红方坦克5", "position": [117.004799, 25.104637, 0], "objectType": "land", "url": "static/models/glb/T99.glb", "image": "static/img/entity/雷达车-红.png", "scale": 100.0, "imageScale": 0.15, "imageOffsetHeading": -90, "billBoardShow": true}}, {"type": "Model", "options": {"id": "红方坦克6", "position": [117.040799, 25.086637134497185, 0], "objectType": "land", "url": "static/models/glb/T99.glb", "image": "static/img/entity/雷达车-红.png", "scale": 100.0, "imageScale": 0.15, "imageOffsetHeading": -90, "billBoardShow": true}}, {"type": "Model", "options": {"id": "红方坦克7", "position": [117.040799, 25.068637, 0], "objectType": "land", "url": "static/models/glb/T99.glb", "image": "static/img/entity/雷达车-红.png", "scale": 100.0, "imageScale": 0.15, "imageOffsetHeading": -90, "billBoardShow": true}}, {"type": "Model", "options": {"id": "红方坦克8", "position": [117.040799, 25.104637, 0], "objectType": "land", "url": "static/models/glb/T99.glb", "image": "static/img/entity/雷达车-红.png", "scale": 100.0, "imageScale": 0.15, "imageOffsetHeading": -90, "billBoardShow": true}}, {"type": "Model", "options": {"id": "红方导弹车0", "position": [117.84681167934484, 25.686747190702327, 0], "objectType": "land", "url": "static/models/MissileVehicle/missile-vehicle.gltf", "image": "static/img/entity/daodanche_Red.png", "scale": 100.0, "imageScale": 1, "billBoardShow": true, "modeloffsetHeading": 180, "effectList": [{"id": "effect_dynnamicLineEffect_0", "type": "dynnamicLineEffect", "destID": "红方地面站0", "positions": [[102, 39, 10000], [105, 39, 10000], [105, 45, 10000]], "color": "rgb(237,102,97)", "alpha": 0.8}, {"id": "effect_dynnamicLineEffect_1", "type": "dynnamicLineEffect", "destID": "红方地面站1", "positions": [[102, 39, 10000], [105, 39, 10000], [105, 45, 10000]], "color": "rgb(237,102,97)", "alpha": 0.8}, {"id": "effect_dynnamicLineEffect_2", "type": "dynnamicLineEffect", "destID": "红方地面站2", "positions": [[102, 39, 10000], [105, 39, 10000], [105, 45, 10000]], "color": "rgb(237,102,97)", "alpha": 0.8}]}}, {"type": "Model", "options": {"id": "红方导弹车1", "position": [117.84681167934484, 25.668747, 0], "objectType": "land", "url": "static/models/MissileVehicle/missile-vehicle.gltf", "image": "static/img/entity/daodanche_Red.png", "scale": 100.0, "modeloffsetHeading": 180, "imageScale": 1, "billBoardShow": true}}, {"type": "Model", "options": {"id": "红方导弹车2", "position": [117.84681167934484, 25.70474, 0], "objectType": "land", "url": "static/models/MissileVehicle/missile-vehicle.gltf", "image": "static/img/entity/daodanche_Red.png", "scale": 100.0, "imageScale": 1, "modeloffsetHeading": 180, "billBoardShow": true}}, {"type": "Model", "options": {"id": "红方导弹车3", "position": [117.864811, 25.686747190702327, 0], "objectType": "land", "url": "static/models/MissileVehicle/missile-vehicle.gltf", "image": "static/img/entity/daodanche_Red.png", "scale": 100.0, "imageScale": 1, "modeloffsetHeading": 180, "billBoardShow": true}}, {"type": "Model", "options": {"id": "红方导弹车4", "position": [117.864811, 25.668747, 0], "objectType": "land", "url": "static/models/MissileVehicle/missile-vehicle.gltf", "image": "static/img/entity/daodanche_Red.png", "scale": 100.0, "imageScale": 1, "modeloffsetHeading": 180, "billBoardShow": true}}, {"type": "Model", "options": {"id": "红方导弹车5", "position": [117.864811, 25.70474, 0], "objectType": "land", "url": "static/models/MissileVehicle/missile-vehicle.gltf", "image": "static/img/entity/daodanche_Red.png", "scale": 100.0, "imageScale": 1, "modeloffsetHeading": 180, "billBoardShow": true}}, {"type": "Model", "options": {"id": "红方导弹车6", "position": [117.828811, 25.686747190702327, 0], "objectType": "land", "url": "static/models/MissileVehicle/missile-vehicle.gltf", "image": "static/img/entity/daodanche_Red.png", "scale": 100.0, "imageScale": 1, "modeloffsetHeading": 180, "billBoardShow": true}}, {"type": "Model", "options": {"id": "红方导弹车7", "position": [117.828811, 25.668747, 0], "objectType": "land", "url": "static/models/MissileVehicle/missile-vehicle.gltf", "image": "static/img/entity/daodanche_Red.png", "scale": 100.0, "imageScale": 1, "modeloffsetHeading": 180, "billBoardShow": true}}, {"type": "Model", "options": {"id": "红方导弹车8", "position": [117.828811, 25.70474, 0], "objectType": "land", "url": "static/models/MissileVehicle/missile-vehicle.gltf", "image": "static/img/entity/daodanche_Red.png", "scale": 100.0, "imageScale": 1, "modeloffsetHeading": 180, "billBoardShow": true}}, {"type": "Model", "options": {"id": "红方地面站0", "position": [117.610788, 24.023933, 0], "objectType": "land", "url": "static/models/gltf/DMLD.gltf", "image": "static/img/entity/DetectStation_Red.png", "scale": 1.0, "maximumScale": 256, "imageScale": 0.15, "billBoardShow": true, "effectList": [{"id": "red_parabolaRadar_00", "type": "parabolaRadar", "show": true, "parabolaHeight": 20000, "parabolaRadius": 50000, "verticalLineCount": 50, "horizontalLineCount": 20, "scanningRate": 180, "scannerColor": "rgba(255,255,0,0.25)", "color": ["rgba(237,102,97,0.5)", "rgba(237,102,97,0.5)"], "outlineColor": ["rgba(237,102,97, 0.8)", "rgba(237,102,97, 0.8)"]}]}}, {"type": "Model", "options": {"id": "红方地面站1", "position": [118.295277, 24.606943, 0], "objectType": "land", "url": "static/models/gltf/DMLD.gltf", "image": "static/img/entity/DetectStation_Red.png", "scale": 1.0, "maximumScale": 256, "imageScale": 0.15, "billBoardShow": true, "effectList": [{"id": "red_parabolaRadar_01", "type": "parabolaRadar", "show": true, "parabolaHeight": 20000, "parabolaRadius": 50000, "verticalLineCount": 50, "horizontalLineCount": 20, "scanningRate": 180, "scannerColor": "rgba(255,255,0,0.25)", "color": ["rgba(237,102,97,0.5)", "rgba(237,102,97,0.5)"], "outlineColor": ["rgba(237,102,97, 0.8)", "rgba(237,102,97, 0.8)"]}]}}, {"type": "Model", "options": {"id": "红方地面站2", "position": [118.975603, 25.156354, 0], "objectType": "land", "url": "static/models/gltf/DMLD.gltf", "image": "static/img/entity/DetectStation_Red.png", "scale": 1.0, "maximumScale": 256, "imageScale": 0.15, "billBoardShow": true, "effectList": [{"id": "red_parabolaRadar_02", "type": "parabolaRadar", "parabolaHeight": 20000, "parabolaRadius": 50000, "verticalLineCount": 50, "horizontalLineCount": 20, "scanningRate": 180, "scannerColor": "rgba(255,255,0,0.25)", "color": ["rgba(237,102,97,0.5)", "rgba(237,102,97,0.5)"], "outlineColor": ["rgba(237,102,97, 0.8)", "rgba(237,102,97, 0.8)"]}]}}, {"type": "Model", "options": {"id": "红方护卫舰0", "position": [119.11353405272256, 24.642720673911807, 0], "objectType": "land", "url": "femap/Data/model/oceanModel/BlackSwan-frigate.glb", "image": "static/img/entity/destroyer_Red.png", "scale": 100.0, "modeloffsetHeading": 0, "imageOffsetHeading": 0, "maximumScale": 256, "imageScale": 0.2, "billBoardShow": true, "drivePosition": [{"time": "2025/03/13 07:00:00", "position": [119.11353405272256, 24.642720673911807, 0]}, {"time": "2025/03/13 07:02:00", "position": [119.3262957547274, 24.533415259289093, 0]}, {"time": "2025/03/13 07:03:00", "position": [119.53091213164309, 24.42977611789587, 5000]}]}}, {"type": "Model", "options": {"id": "红方护卫舰1", "position": [118.97139151804636, 24.397231506639095, 0], "objectType": "land", "url": "femap/Data/model/oceanModel/BlackSwan-frigate.glb", "image": "static/img/entity/destroyer_Red.png", "scale": 100.0, "modeloffsetHeading": 0, "imageOffsetHeading": 0, "maximumScale": 256, "imageScale": 0.2, "billBoardShow": true, "drivePosition": [{"time": "2025/03/13 07:00:00", "position": [118.97139151804636, 24.397231506639095, 0]}, {"time": "2025/03/13 07:02:00", "position": [119.18104958274878, 24.29109053397706, 0]}, {"time": "2025/03/13 07:03:00", "position": [119.3751498179418, 24.188398984528146, 5000]}]}}, {"type": "Model", "options": {"id": "红方战舰", "position": [119.24529602471274, 24.420077536046023, 0], "objectType": "land", "url": "femap/Data/model/oceanModel/fightWarship.glb", "image": "static/img/entity/destroyer_Red.png", "scale": 100.0, "modeloffsetHeading": 0, "imageOffsetHeading": 0, "maximumScale": 256, "imageScale": 0.2, "billBoardShow": true, "effectList": [{"id": "effect_dynnamicLineEffect_0", "type": "dynnamicLineEffect", "destID": "红方护卫舰0", "positions": [[102, 39, 10000], [105, 39, 10000], [105, 45, 10000]], "color": "rgb(237,102,97)", "alpha": 0.8}, {"id": "effect_dynnamicLineEffect_1", "type": "dynnamicLineEffect", "destID": "红方护卫舰1", "positions": [[102, 39, 10000], [105, 39, 10000], [105, 45, 10000]], "color": "rgb(237,102,97)", "alpha": 0.8}], "drivePosition": [{"time": "2025/03/13 07:00:00", "position": [119.24529602471274, 24.420077536046023, 0]}, {"time": "2025/03/13 07:01:24", "position": [119.41161630559134, 24.330547572888555, 0]}, {"time": "2025/03/13 07:02:00", "position": [119.45735613586702, 24.305570429326576, 0]}, {"time": "2025/03/13 07:03:00", "position": [119.6691431640678, 24.20364489135076, 0]}]}}, {"type": "Model", "options": {"id": "红方预警机0", "position": [119.3262957547274, 24.533415259289093, 5000], "objectType": "sky", "url": "static/models/glb/KJ2000.glb", "image": "static/img/entity/Awacs_Red.png", "scale": 100.0, "modeloffsetHeading": 90, "imageOffsetHeading": 0, "maximumScale": 256, "imageScale": 0.2, "billBoardShow": true, "effectList": [{"id": "effect_ringRadar", "type": "ringRadar", "show": true, "radarHeight": 2000, "sphericalRadius": 12000, "bottomRadius": 12000, "sphericalSurfaceLineCount": 8, "planeSurfaceLineCount": 50, "scannerColor": "rgba(255,255,0,0.2)", "color": "rgba(237,102,97,0.5)", "outlineColor": "rgba(237,102,97,0.8)"}, {"id": "effect_dynnamicLineEffect_0", "type": "dynnamicLineEffect", "destID": "红方战舰", "positions": [[102, 39, 10000], [105, 39, 10000], [105, 45, 10000]], "color": "rgb(237,102,97)", "alpha": 0.8}], "drivePosition": [{"time": "2025/03/13 07:00:00", "position": [119.3262957547274, 24.533415259289093, 5000]}, {"time": "2025/03/13 07:02:00", "position": [119.53091213164309, 24.42977611789587, 5000]}, {"time": "2025/03/13 07:03:00", "position": [119.7659713239897, 24.3220845369989, 5000]}]}}, {"type": "Model", "options": {"id": "红方预警机1", "position": [119.18104958274878, 24.29109053397706, 5000], "objectType": "sky", "url": "static/models/glb/KJ2000.glb", "image": "static/img/entity/Awacs_Red.png", "scale": 100.0, "modeloffsetHeading": 90, "imageOffsetHeading": 0, "maximumScale": 256, "imageScale": 0.2, "billBoardShow": true, "effectList": [{"id": "effect_ringRadar", "type": "ringRadar", "show": true, "radarHeight": 2000, "sphericalRadius": 12000, "bottomRadius": 12000, "sphericalSurfaceLineCount": 8, "planeSurfaceLineCount": 50, "scannerColor": "rgba(255,255,0,0.2)", "color": "rgba(237,102,97,0.5)", "outlineColor": "rgba(237,102,97,0.8)"}, {"id": "effect_dynnamicLineEffect_0", "type": "dynnamicLineEffect", "destID": "红方战舰", "positions": [[102, 39, 10000], [105, 39, 10000], [105, 45, 10000]], "color": "rgb(237,102,97)", "alpha": 0.8}], "drivePosition": [{"time": "2025/03/13 07:00:00", "position": [119.18104958274878, 24.29109053397706, 5000]}, {"time": "2025/03/13 07:02:00", "position": [119.3751498179418, 24.188398984528146, 5000]}, {"time": "2025/03/13 07:03:00", "position": [119.58812110230457, 24.071031214069574, 5000]}]}}, {"type": "Model", "options": {"id": "红方预警机2", "position": [119.53091213164309, 24.42977611789587, 5000], "objectType": "sky", "url": "static/models/glb/KJ2000.glb", "image": "static/img/entity/Awacs_Red.png", "scale": 100.0, "modeloffsetHeading": 90, "imageOffsetHeading": 0, "maximumScale": 256, "imageScale": 0.2, "billBoardShow": true, "effectList": [{"id": "effect_ringRadar", "type": "ringRadar", "show": true, "radarHeight": 2000, "sphericalRadius": 12000, "bottomRadius": 12000, "sphericalSurfaceLineCount": 8, "planeSurfaceLineCount": 50, "scannerColor": "rgba(255,255,0,0.2)", "color": "rgba(237,102,97,0.5)", "outlineColor": "rgba(237,102,97,0.8)"}, {"id": "effect_dynnamicLineEffect_0", "type": "dynnamicLineEffect", "destID": "红方战舰", "positions": [[102, 39, 10000], [105, 39, 10000], [105, 45, 10000]], "color": "rgb(237,102,97)", "alpha": 0.8}, {"id": "effect_aimEffect", "type": "AimEffect", "destID": "蓝方战斗机0", "color": "rgba(255,0,0,0.1)", "lineColor": "rgba(255,255,255,1.0)", "visible": true}], "drivePosition": [{"time": "2025/03/13 07:00:00", "position": [119.53091213164309, 24.42977611789587, 5000]}, {"time": "2025/03/13 07:02:00", "position": [119.7659713239897, 24.3220845369989, 5000]}, {"time": "2025/03/13 07:03:00", "position": [120.03693663010081, 24.188886887056295, 5000]}]}}, {"type": "Model", "options": {"id": "红方预警机3", "position": [119.3751498179418, 24.188398984528146, 5000], "objectType": "sky", "url": "static/models/glb/KJ2000.glb", "image": "static/img/entity/Awacs_Red.png", "scale": 100.0, "modeloffsetHeading": 90, "imageOffsetHeading": 0, "maximumScale": 256, "imageScale": 0.2, "billBoardShow": true, "effectList": [{"id": "effect_ringRadar", "type": "ringRadar", "show": true, "radarHeight": 2000, "sphericalRadius": 12000, "bottomRadius": 12000, "sphericalSurfaceLineCount": 8, "planeSurfaceLineCount": 50, "scannerColor": "rgba(255,255,0,0.2)", "color": "rgba(237,102,97,0.5)", "outlineColor": "rgba(237,102,97,0.8)"}, {"id": "effect_dynnamicLineEffect_0", "type": "dynnamicLineEffect", "destID": "红方战舰", "positions": [[102, 39, 10000], [105, 39, 10000], [105, 45, 10000]], "color": "rgb(237,102,97)", "alpha": 0.8}, {"id": "effect_aimEffect", "type": "AimEffect", "destID": "蓝方战斗机0", "color": "rgba(255,0,0,0.1)", "lineColor": "rgba(255,255,255,1.0)", "visible": true}], "drivePosition": [{"time": "2025/03/13 07:00:00", "position": [119.3751498179418, 24.188398984528146, 5000]}, {"time": "2025/03/13 07:02:00", "position": [119.58812110230457, 24.071031214069574, 5000]}, {"time": "2025/03/13 07:03:00", "position": [119.87054087784563, 23.921393963870116, 5000]}]}}, {"type": "Model", "options": {"id": "蓝方地面站0", "position": [120.2303177505845, 23.599431009400444, 0], "objectType": "land", "url": "static/models/gltf/DMLD.gltf", "image": "static/img/entity/DetectStation_blue.png", "scale": 1.0, "maximumScale": 128, "imageScale": 0.15, "billBoardShow": true, "effectList": [{"id": "blue_parabolaRadar", "type": "parabolaRadar", "parabolaHeight": 10000, "parabolaRadius": 30000, "verticalLineCount": 50, "horizontalLineCount": 20, "scanningRate": 180, "scannerColor": "rgba(255,255,0,0.25)", "color": ["rgba(49, 180, 170, 0.5)", "rgba(49, 180, 170, 0.5)"], "outlineColor": ["rgba(49, 180, 170, 0.8)", "rgba(49, 180, 170, 0.8)"]}]}}, {"type": "Model", "options": {"id": "蓝方地面站1", "position": [120.46993955880593, 24.135157967918733, 0], "objectType": "land", "url": "static/models/gltf/DMLD.gltf", "image": "static/img/entity/DetectStation_blue.png", "scale": 1.0, "maximumScale": 128, "imageScale": 0.15, "billBoardShow": true, "effectList": [{"id": "blue_parabolaRadar", "type": "parabolaRadar", "parabolaHeight": 10000, "parabolaRadius": 30000, "verticalLineCount": 50, "horizontalLineCount": 20, "scanningRate": 180, "scannerColor": "rgba(255,255,0,0.25)", "color": ["rgba(49, 180, 170, 0.5)", "rgba(49, 180, 170, 0.5)"], "outlineColor": ["rgba(49, 180, 170, 0.8)", "rgba(49, 180, 170, 0.8)"]}]}}, {"type": "Wall", "options": {"id": "蓝方导弹阵地", "center": [120.57945547681348, 23.702700568676875], "radius": 5000, "height": 1000, "fillElevation": [0, 0.3], "color": ["rgb(92, 199, 246)", "rgb(92, 199, 246)"], "alpha": 0.5, "show": true, "fill": true}}, {"type": "Wall", "options": {"id": "蓝方坦克阵地", "center": [120.64393544314127, 23.82663577222821], "radius": 5000, "height": 1000, "fillElevation": [0, 0.3], "color": ["rgb(92, 199, 246)", "rgb(92, 199, 246)"], "alpha": 0.5, "show": true, "fill": true}}, {"type": "Model", "options": {"id": "蓝方坦克0", "position": [120.64393544314127, 23.82663577222821, 0], "objectType": "land", "url": "static/models/glb/T99.glb", "image": "static/img/entity/tanke_Blue.png", "scale": 100.0, "imageScale": 1, "billBoardShow": true, "modeloffsetHeading": 180, "imageOffsetHeading": 90, "effectList": [{"id": "effect_dynnamicLineEffect_0", "type": "dynnamicLineEffect", "destID": "蓝方地面站0", "positions": [[102, 39, 10000], [105, 39, 10000], [105, 45, 10000]], "color": "rgb(49, 180, 170)", "alpha": 0.8}, {"id": "effect_dynnamicLineEffect_1", "type": "dynnamicLineEffect", "destID": "蓝方地面站1", "positions": [[102, 39, 10000], [105, 39, 10000], [105, 45, 10000]], "color": "rgb(49, 180, 170)", "alpha": 0.8}]}}, {"type": "Model", "options": {"id": "蓝方坦克1", "position": [120.64393544314127, 23.844635, 0], "objectType": "land", "url": "static/models/glb/T99.glb", "image": "static/img/entity/tanke_Blue.png", "scale": 100.0, "imageScale": 1, "billBoardShow": true, "modeloffsetHeading": 180, "imageOffsetHeading": 90}}, {"type": "Model", "options": {"id": "蓝方坦克2", "position": [120.64393544314127, 23.808635, 0], "objectType": "land", "url": "static/models/glb/T99.glb", "image": "static/img/entity/tanke_Blue.png", "scale": 100.0, "imageScale": 1, "modeloffsetHeading": 180, "imageOffsetHeading": 90, "billBoardShow": true}}, {"type": "Model", "options": {"id": "蓝方坦克3", "position": [120.661935, 23.82663577222821, 0], "objectType": "land", "url": "static/models/glb/T99.glb", "image": "static/img/entity/tanke_Blue.png", "scale": 100.0, "imageScale": 1, "modeloffsetHeading": 180, "imageOffsetHeading": 90, "billBoardShow": true}}, {"type": "Model", "options": {"id": "蓝方坦克4", "position": [120.661935, 23.844635, 0], "objectType": "land", "url": "static/models/glb/T99.glb", "image": "static/img/entity/tanke_Blue.png", "scale": 100.0, "imageScale": 1, "modeloffsetHeading": 180, "imageOffsetHeading": 90, "billBoardShow": true}}, {"type": "Model", "options": {"id": "蓝方坦克5", "position": [120.661935, 23.808635, 0], "objectType": "land", "url": "static/models/glb/T99.glb", "image": "static/img/entity/tanke_Blue.png", "scale": 100.0, "imageScale": 1, "modeloffsetHeading": 180, "imageOffsetHeading": 90, "billBoardShow": true}}, {"type": "Model", "options": {"id": "蓝方坦克6", "position": [120.625935, 23.82663577222821, 0], "objectType": "land", "url": "static/models/glb/T99.glb", "image": "static/img/entity/tanke_Blue.png", "scale": 100.0, "imageScale": 1, "modeloffsetHeading": 180, "imageOffsetHeading": 90, "billBoardShow": true}}, {"type": "Model", "options": {"id": "蓝方坦克7", "position": [120.625935, 23.844635, 0], "objectType": "land", "url": "static/models/glb/T99.glb", "image": "static/img/entity/tanke_Blue.png", "scale": 100.0, "imageScale": 1, "modeloffsetHeading": 180, "imageOffsetHeading": 90, "billBoardShow": true}}, {"type": "Model", "options": {"id": "蓝方坦克8", "position": [120.625935, 23.808635, 0], "objectType": "land", "url": "static/models/glb/T99.glb", "image": "static/img/entity/tanke_Blue.png", "scale": 100.0, "imageScale": 1, "modeloffsetHeading": 180, "imageOffsetHeading": 90, "billBoardShow": true}}, {"type": "Model", "options": {"id": "蓝方导弹车0", "position": [120.57945547681348, 23.702700568676875, 0], "objectType": "land", "url": "static/models/MissileVehicle/missile-vehicle.gltf", "image": "static/img/entity/daodanche_Blue.png", "scale": 100.0, "imageScale": 1, "billBoardShow": true, "effectList": [{"id": "effect_dynnamicLineEffect_0", "type": "dynnamicLineEffect", "destID": "蓝方地面站0", "positions": [[102, 39, 10000], [105, 39, 10000], [105, 45, 10000]], "color": "rgb(49, 180, 170)", "alpha": 0.8}, {"id": "effect_dynnamicLineEffect_1", "type": "dynnamicLineEffect", "destID": "蓝方地面站1", "positions": [[102, 39, 10000], [105, 39, 10000], [105, 45, 10000]], "color": "rgb(49, 180, 170)", "alpha": 0.8}]}}, {"type": "Model", "options": {"id": "蓝方导弹车1", "position": [120.57945547681348, 23.7207, 0], "objectType": "land", "url": "static/models/MissileVehicle/missile-vehicle.gltf", "image": "static/img/entity/daodanche_Blue.png", "scale": 100.0, "imageScale": 1, "billBoardShow": true}}, {"type": "Model", "options": {"id": "蓝方导弹车2", "position": [120.57945547681348, 23.6847, 0], "objectType": "land", "url": "static/models/MissileVehicle/missile-vehicle.gltf", "image": "static/img/entity/daodanche_Blue.png", "scale": 100.0, "imageScale": 1, "billBoardShow": true}}, {"type": "Model", "options": {"id": "蓝方导弹车3", "position": [120.597455, 23.702700568676875, 0], "objectType": "land", "url": "static/models/MissileVehicle/missile-vehicle.gltf", "image": "static/img/entity/daodanche_Blue.png", "scale": 100.0, "imageScale": 1, "billBoardShow": true}}, {"type": "Model", "options": {"id": "蓝方导弹车4", "position": [120.597455, 23.7207, 0], "objectType": "land", "url": "static/models/MissileVehicle/missile-vehicle.gltf", "image": "static/img/entity/daodanche_Blue.png", "scale": 100.0, "imageScale": 1, "billBoardShow": true}}, {"type": "Model", "options": {"id": "蓝方导弹车5", "position": [120.597455, 23.6847, 0], "objectType": "land", "url": "static/models/MissileVehicle/missile-vehicle.gltf", "image": "static/img/entity/daodanche_Blue.png", "scale": 100.0, "imageScale": 1, "billBoardShow": true}}, {"type": "Model", "options": {"id": "蓝方导弹车6", "position": [120.561455, 23.702700568676875, 0], "objectType": "land", "url": "static/models/MissileVehicle/missile-vehicle.gltf", "image": "static/img/entity/daodanche_Blue.png", "scale": 100.0, "imageScale": 1, "billBoardShow": true}}, {"type": "Model", "options": {"id": "蓝方导弹车7", "position": [120.561455, 23.7207, 0], "objectType": "land", "url": "static/models/MissileVehicle/missile-vehicle.gltf", "image": "static/img/entity/daodanche_Blue.png", "scale": 100.0, "imageScale": 1, "billBoardShow": true}}, {"type": "Model", "options": {"id": "蓝方导弹车8", "position": [120.561455, 23.6847, 0], "objectType": "land", "url": "static/models/MissileVehicle/missile-vehicle.gltf", "image": "static/img/entity/daodanche_Blue.png", "scale": 100.0, "imageScale": 1, "billBoardShow": true}}, {"type": "Model", "options": {"id": "红方战斗机0", "position": [119.5274914945852, 24.65002228744795, 5000], "objectType": "sky", "url": "static/models/gltf/j20.gltf", "image": "static/img/entity/fighter_Red.png", "scale": 100.0, "imageScale": 0.15, "billBoardShow": true, "modeloffsetHeading": 90, "imageOffsetHeading": 0, "effectList": [{"id": "effect_ribbon", "type": "Ribbon", "maxAlpha": 1.0, "minAlpha": 0.1, "width": 1000, "maxVerticeNum": 300, "offset": [-10, 0, 0], "color": "rgb(255,0,0)"}], "drivePosition": [{"time": "2025/03/13 07:00:00", "position": [119.5274914945852, 24.65002228744795, 5000]}, {"time": "2025/03/13 07:01:00", "position": [119.53584175451114, 24.64839099154966, 5000]}, {"time": "2025/03/13 07:01:10", "position": [119.81592630929403, 24.560474388841673, 5000]}, {"time": "2025/03/13 07:01:20", "position": [120.12436114427355, 24.397637632518123, 5000]}, {"time": "2025/03/13 07:01:30", "position": [120.42115000213965, 24.17746704725236, 5000]}, {"time": "2025/03/13 07:02:00", "position": [120.57059887203457, 24.06005796275758, 5000]}]}}, {"type": "Model", "options": {"id": "红方战斗机1", "position": [119.16768495392822, 24.080359582583473, 5000], "objectType": "sky", "url": "static/models/gltf/j20.gltf", "image": "static/img/entity/fighter_Red.png", "scale": 100.0, "imageScale": 0.15, "billBoardShow": true, "modeloffsetHeading": 90, "imageOffsetHeading": 0, "effectList": [{"id": "effect_ribbon", "type": "Ribbon", "maxAlpha": 1.0, "minAlpha": 0.1, "width": 1000, "maxVerticeNum": 300, "offset": [-10, 0, 0], "color": "rgb(255,0,0)"}], "drivePosition": [{"time": "2025/03/13 07:00:00", "position": [119.16768495392822, 24.080359582583473, 5000]}, {"time": "2025/03/13 07:01:00", "position": [119.17654144900543, 24.075396376246488, 5000]}, {"time": "2025/03/13 07:01:10", "position": [119.53875270981634, 23.855747917176213, 5000]}, {"time": "2025/03/13 07:01:20", "position": [119.96587366083423, 23.627299912679717, 5000]}, {"time": "2025/03/13 07:02:00", "position": [120.36043890099786, 23.40943773565224, 5000]}]}}, {"type": "Model", "options": {"id": "蓝方战斗机0", "position": [120.14751973131419, 23.828205727572474, 5000], "objectType": "sky", "url": "static/models/glb/J15.glb", "image": "static/img/entity/fighter_Blue.png", "scale": 100.0, "imageScale": 0.15, "modeloffsetHeading": 90, "imageOffsetHeading": 0, "billBoardShow": true, "effectList": [{"id": "effect_ribbon", "type": "Ribbon", "maxAlpha": 1.0, "minAlpha": 0.1, "width": 1000, "maxVerticeNum": 300, "offset": [-10, 0, 0], "color": "rgb(49, 180, 170)"}], "drivePosition": [{"time": "2025/03/13 07:00:00", "position": [120.14751973131419, 23.828205727572474, 5000]}, {"time": "2025/03/13 07:01:00", "position": [120.13439264647081, 23.841494301641745, 5000]}, {"time": "2025/03/13 07:01:26", "position": [120.03177304561653, 23.99109704521365, 5000]}, {"time": "2025/03/13 07:01:30", "position": [119.79365132838595, 24.1339532741043, 5000]}]}}, {"type": "Model", "options": {"id": "红方导弹0", "position": [120.12436114427355, 24.397637632518123, 5000], "objectType": "sky", "url": "static/models/glb/missile.glb", "image": "static/img/entity/Missile_Red.png", "scale": 50.0, "modeloffsetHeading": 90, "imageScale": 0.6, "effectList": [{"id": "effect_particle", "type": "particle"}], "drivePosition": [{"time": "2025/03/13 07:01:20", "position": [120.12436114427355, 24.397637632518123, 5000]}, {"time": "2025/03/13 07:01:22", "position": [120.46993955880593, 24.135157967918733, 0]}]}}, {"type": "Model", "options": {"id": "红方导弹1", "position": [119.96587366083423, 23.627299912679717, 5000], "objectType": "sky", "url": "static/models/glb/missile.glb", "image": "static/img/entity/Missile_Red.png", "scale": 50.0, "modeloffsetHeading": 90, "imageScale": 0.6, "effectList": [{"id": "effect_particle", "type": "particle"}], "drivePosition": [{"time": "2025/03/13 07:01:20", "position": [119.96587366083423, 23.627299912679717, 5000]}, {"time": "2025/03/13 07:01:30", "position": [120.2303177505845, 23.599431009400444, 0]}]}}, {"type": "Model", "options": {"id": "红方导弹2", "position": [120.12436114427355, 24.397637632518123, 5000], "objectType": "sky", "url": "static/models/glb/missile.glb", "image": "static/img/entity/Missile_Red.png", "scale": 50.0, "modeloffsetHeading": 90, "imageScale": 0.6, "effectList": [{"id": "effect_particle", "type": "particle"}], "drivePosition": [{"time": "2025/03/13 07:01:24", "position": [119.41161630559134, 24.330547572888555, 0]}, {"time": "2025/03/13 07:01:26", "position": [120.03177304561653, 23.99109704521365, 5000]}]}}, {"type": "Model", "options": {"id": "红方导弹3", "position": [120.42115000213965, 24.17746704725236, 5000], "objectType": "sky", "url": "static/models/glb/missile.glb", "image": "static/img/entity/Missile_Red.png", "scale": 50.0, "modeloffsetHeading": 90, "imageScale": 0.6, "effectList": [{"id": "effect_particle", "type": "particle"}], "drivePosition": [{"time": "2025/03/13 07:01:30", "position": [120.42115000213965, 24.17746704725236, 5000]}, {"time": "2025/03/13 07:01:32", "position": [120.64393544314127, 23.82663577222821, 0]}]}}, {"type": "Model", "options": {"id": "红方导弹4", "position": [120.42115000213965, 24.17746704725236, 5000], "objectType": "sky", "url": "static/models/glb/missile.glb", "image": "static/img/entity/Missile_Red.png", "scale": 50.0, "modeloffsetHeading": 90, "imageScale": 0.6, "effectList": [{"id": "effect_particle", "type": "particle"}], "drivePosition": [{"time": "2025/03/13 07:01:30", "position": [120.42115000213965, 24.17746704725236, 5000]}, {"time": "2025/03/13 07:01:32", "position": [120.57945547681348, 23.702700568676875, 0]}]}}, {"type": "Label", "options": {"position": [117.84681167934484, 25.686747190702327, 0], "id": "红方导弹阵地", "name": "红方导弹阵地", "font": "18px 黑体", "fillColor": "rgb(255,0,0)", "outlineColor": "rgb(0,0,0)", "outlineWidth": 3, "style": 2, "visibleDistance": [0, 185000], "disableDepthTestDistance": false}}, {"type": "Label", "options": {"position": [117.02279931670907, 25.086637134497185, 0], "id": "红方坦克阵地", "name": "红方坦克阵地", "font": "18px 黑体", "fillColor": "rgb(255,0,0)", "outlineColor": "rgb(0,0,0)", "outlineWidth": 3, "style": 2, "visibleDistance": [0, 185000], "disableDepthTestDistance": false}}, {"type": "Label", "options": {"position": [120.64393544314127, 23.82663577222821, 0], "id": "蓝方坦克阵地", "name": "蓝方坦克阵地", "font": "18px 黑体", "fillColor": "rgb(92,199,246)", "outlineColor": "rgb(0,0,0)", "outlineWidth": 3, "style": 2, "visibleDistance": [0, 185000], "disableDepthTestDistance": false}}, {"type": "Label", "options": {"position": [120.57945547681348, 23.702700568676875, 0], "id": "蓝方导弹阵地", "name": "蓝方导弹阵地", "font": "18px 黑体", "fillColor": "rgb(92,199,246)", "outlineColor": "rgb(0,0,0)", "outlineWidth": 3, "style": 2, "visibleDistance": [0, 185000], "disableDepthTestDistance": false}}, {"type": "Label", "options": {"position": [120.14751973131419, 23.828205727572474, 5000], "id": "蓝方战斗机", "name": "蓝方战斗机", "font": "18px 黑体", "fillColor": "rgb(92,199,246)", "outlineColor": "rgb(0,0,0)", "outlineWidth": 3, "style": 2, "visibleDistance": [0, 185000], "disableDepthTestDistance": false}}, {"type": "Label", "options": {"position": [119.12590610924464, 24.480447834801854, 0], "id": "红方舰队", "name": "红方舰队", "font": "18px 黑体", "fillColor": "rgb(255,0,0)", "outlineColor": "rgb(0,0,0)", "outlineWidth": 3, "style": 2, "visibleDistance": [0, 185000], "disableDepthTestDistance": false}}, {"type": "Label", "options": {"position": [119.36775374126267, 24.36432130718516, 5000], "id": "红方预警机编队", "name": "红方预警机编队", "font": "18px 黑体", "fillColor": "rgb(255,0,0)", "outlineColor": "rgb(0,0,0)", "outlineWidth": 3, "style": 2, "visibleDistance": [0, 185000], "disableDepthTestDistance": false}}, {"type": "Label", "options": {"position": [119.5274914945852, 24.65002228744795, 5000], "id": "红方战斗机0", "name": "红方战斗机", "font": "18px 黑体", "fillColor": "rgb(255,0,0)", "outlineColor": "rgb(0,0,0)", "outlineWidth": 3, "style": 2, "visibleDistance": [0, 185000], "disableDepthTestDistance": false}}, {"type": "Label", "options": {"position": [119.16768495392822, 24.080359582583473, 5000], "id": "红方战斗机1", "name": "红方战斗机", "font": "18px 黑体", "fillColor": "rgb(255,0,0)", "outlineColor": "rgb(0,0,0)", "outlineWidth": 3, "style": 2, "visibleDistance": [0, 185000], "disableDepthTestDistance": false}}, {"type": "Label", "options": {"position": [117.610788, 24.023933, 0], "id": "红方地面站0", "name": "红方地面站", "font": "18px 黑体", "fillColor": "rgb(255,0,0)", "outlineColor": "rgb(0,0,0)", "outlineWidth": 3, "style": 2, "visibleDistance": [0, 185000], "disableDepthTestDistance": false}}, {"type": "Label", "options": {"position": [118.295277, 24.606943, 0], "id": "红方地面站1", "name": "红方地面站", "font": "18px 黑体", "fillColor": "rgb(255,0,0)", "outlineColor": "rgb(0,0,0)", "outlineWidth": 3, "style": 2, "visibleDistance": [0, 185000], "disableDepthTestDistance": false}}, {"type": "Label", "options": {"position": [118.975603, 25.156354, 0], "id": "红方地面站2", "name": "红方地面站", "font": "18px 黑体", "fillColor": "rgb(255,0,0)", "outlineColor": "rgb(0,0,0)", "outlineWidth": 3, "style": 2, "visibleDistance": [0, 185000], "disableDepthTestDistance": false}}, {"type": "Label", "options": {"position": [120.2303177505845, 23.599431009400444, 0], "id": "蓝方地面站0", "name": "蓝方地面站", "font": "18px 黑体", "fillColor": "rgb(92,199,246)", "outlineColor": "rgb(0,0,0)", "outlineWidth": 3, "style": 2, "visibleDistance": [0, 185000], "disableDepthTestDistance": false}}, {"type": "Label", "options": {"position": [120.46993955880593, 24.135157967918733, 0], "id": "蓝方地面站1", "name": "蓝方地面站", "font": "18px 黑体", "fillColor": "rgb(92,199,246)", "outlineColor": "rgb(0,0,0)", "outlineWidth": 3, "style": 2, "visibleDistance": [0, 185000], "disableDepthTestDistance": false}}], "visibleList": [{"type": "effect", "entityID": "红方地面站0", "effectID": "red_parabolaRadar_00", "showTime": "2025/03/13 07:01:00"}, {"type": "effect", "entityID": "红方地面站1", "effectID": "red_parabolaRadar_01", "showTime": "2025/03/13 07:01:00"}, {"type": "effect", "entityID": "红方地面站2", "effectID": "red_parabolaRadar_02", "showTime": "2025/03/13 07:01:00"}, {"type": "effect", "entityID": "红方坦克0", "effectID": "effect_dynnamicLineEffect_0", "showTime": "2025/03/13 07:01:00"}, {"type": "effect", "entityID": "红方坦克0", "effectID": "effect_dynnamicLineEffect_1", "showTime": "2025/03/13 07:01:00"}, {"type": "effect", "entityID": "红方坦克0", "effectID": "effect_dynnamicLineEffect_2", "showTime": "2025/03/13 07:01:00"}, {"type": "effect", "entityID": "红方导弹车0", "effectID": "effect_dynnamicLineEffect_0", "showTime": "2025/03/13 07:01:00"}, {"type": "effect", "entityID": "红方导弹车0", "effectID": "effect_dynnamicLineEffect_1", "showTime": "2025/03/13 07:01:00"}, {"type": "effect", "entityID": "红方导弹车0", "effectID": "effect_dynnamicLineEffect_2", "showTime": "2025/03/13 07:01:00"}, {"type": "effect", "entityID": "蓝方地面站0", "effectID": "blue_parabolaRadar", "showTime": "2025/03/13 07:01:00", "hideTime": "2025/03/13 07:01:30"}, {"type": "model", "entityID": "蓝方地面站0", "showTime": "2025/03/13 07:00:00", "hideTime": "2025/03/13 07:01:30"}, {"type": "model", "entityID": "蓝方地面站1", "showTime": "2025/03/13 07:00:00", "hideTime": "2025/03/13 07:01:22"}, {"type": "effect", "entityID": "蓝方地面站1", "effectID": "blue_parabolaRadar", "showTime": "2025/03/13 07:01:00", "hideTime": "2025/03/13 07:01:22"}, {"type": "effect", "entityID": "蓝方坦克0", "effectID": "effect_dynnamicLineEffect_0", "showTime": "2025/03/13 07:01:00", "hideTime": "2025/03/13 07:01:30"}, {"type": "effect", "entityID": "蓝方坦克0", "effectID": "effect_dynnamicLineEffect_1", "showTime": "2025/03/13 07:01:00", "hideTime": "2025/03/13 07:01:22"}, {"type": "effect", "entityID": "蓝方导弹车0", "effectID": "effect_dynnamicLineEffect_0", "showTime": "2025/03/13 07:01:00", "hideTime": "2025/03/13 07:01:30"}, {"type": "effect", "entityID": "蓝方导弹车0", "effectID": "effect_dynnamicLineEffect_1", "showTime": "2025/03/13 07:01:00", "hideTime": "2025/03/13 07:01:22"}, {"type": "effect", "entityID": "红方战舰", "effectID": "effect_dynnamicLineEffect_0", "showTime": "2025/03/13 07:01:00"}, {"type": "effect", "entityID": "红方战舰", "effectID": "effect_dynnamicLineEffect_1", "showTime": "2025/03/13 07:01:00"}, {"type": "effect", "entityID": "红方预警机0", "effectID": "effect_dynnamicLineEffect_0", "showTime": "2025/03/13 07:01:00"}, {"type": "effect", "entityID": "红方预警机1", "effectID": "effect_dynnamicLineEffect_0", "showTime": "2025/03/13 07:01:00"}, {"type": "effect", "entityID": "红方预警机2", "effectID": "effect_aimEffect", "showTime": "2025/03/13 07:01:24", "hideTime": "2025/03/13 07:01:26"}, {"type": "effect", "entityID": "红方预警机3", "effectID": "effect_aimEffect", "showTime": "2025/03/13 07:01:24", "hideTime": "2025/03/13 07:01:26"}, {"type": "effect", "entityID": "红方预警机0", "effectID": "effect_ringRadar", "showTime": "2025/03/13 07:01:00"}, {"type": "effect", "entityID": "红方预警机1", "effectID": "effect_ringRadar", "showTime": "2025/03/13 07:01:00"}, {"type": "effect", "entityID": "红方预警机2", "effectID": "effect_ringRadar", "showTime": "2025/03/13 07:01:00"}, {"type": "effect", "entityID": "红方预警机3", "effectID": "effect_ringRadar", "showTime": "2025/03/13 07:01:00"}, {"type": "effect", "entityID": "红方预警机2", "effectID": "effect_dynnamicLineEffect_0", "showTime": "2025/03/13 07:01:00"}, {"type": "effect", "entityID": "红方预警机3", "effectID": "effect_dynnamicLineEffect_0", "showTime": "2025/03/13 07:01:00"}, {"type": "model", "entityID": "红方导弹0", "showTime": "2025/03/13 07:01:20", "hideTime": "2025/03/13 07:01:22"}, {"type": "effect", "entityID": "红方导弹0", "effectID": "effect_particle", "showTime": "2025/03/13 07:01:22", "hideTime": "2025/03/13 07:01:28"}, {"type": "effect", "entityID": "红方导弹2", "effectID": "effect_particle", "showTime": "2025/03/13 07:01:26", "hideTime": "2025/03/13 07:01:30"}, {"type": "effect", "entityID": "红方导弹3", "effectID": "effect_particle", "showTime": "2025/03/13 07:01:32", "hideTime": "2025/03/13 07:01:40"}, {"type": "effect", "entityID": "红方导弹4", "effectID": "effect_particle", "showTime": "2025/03/13 07:01:32", "hideTime": "2025/03/13 07:01:40"}, {"type": "model", "entityID": "红方导弹1", "showTime": "2025/03/13 07:01:20", "hideTime": "2025/03/13 07:01:30"}, {"type": "model", "entityID": "红方导弹2", "showTime": "2025/03/13 07:01:24", "hideTime": "2025/03/13 07:01:26"}, {"type": "model", "entityID": "红方导弹3", "showTime": "2025/03/13 07:01:30", "hideTime": "2025/03/13 07:01:32"}, {"type": "model", "entityID": "红方导弹4", "showTime": "2025/03/13 07:01:30", "hideTime": "2025/03/13 07:01:32"}, {"type": "model", "entityID": "红方导弹4", "showTime": "2025/03/13 07:01:30", "hideTime": "2025/03/13 07:01:32"}, {"type": "model", "entityID": "蓝方战斗机0", "hideTime": "2025/03/13 07:01:26"}, {"type": "effect", "entityID": "红方导弹1", "effectID": "effect_particle", "showTime": "2025/03/13 07:01:30", "hideTime": "2025/03/13 07:01:34"}, {"type": "label", "entityID": "蓝方地面站0", "hideTime": "2025/03/13 07:01:30"}, {"type": "label", "entityID": "蓝方地面站1", "hideTime": "2025/03/13 07:01:22"}, {"type": "label", "entityID": "蓝方战斗机", "hideTime": "2025/03/13 07:01:00"}, {"type": "label", "entityID": "红方战斗机0", "hideTime": "2025/03/13 07:01:00"}, {"type": "label", "entityID": "红方战斗机1", "hideTime": "2025/03/13 07:01:00"}, {"type": "label", "entityID": "蓝方坦克阵地", "hideTime": "2025/03/13 07:01:32"}, {"type": "label", "entityID": "蓝方导弹阵地", "hideTime": "2025/03/13 07:01:32"}, {"type": "model", "entityID": "蓝方坦克0", "hideTime": "2025/03/13 07:01:32"}, {"type": "model", "entityID": "蓝方坦克1", "hideTime": "2025/03/13 07:01:32"}, {"type": "model", "entityID": "蓝方坦克2", "hideTime": "2025/03/13 07:01:32"}, {"type": "model", "entityID": "蓝方坦克3", "hideTime": "2025/03/13 07:01:32"}, {"type": "model", "entityID": "蓝方坦克4", "hideTime": "2025/03/13 07:01:32"}, {"type": "model", "entityID": "蓝方坦克5", "hideTime": "2025/03/13 07:01:32"}, {"type": "model", "entityID": "蓝方坦克6", "hideTime": "2025/03/13 07:01:32"}, {"type": "model", "entityID": "蓝方坦克7", "hideTime": "2025/03/13 07:01:32"}, {"type": "model", "entityID": "蓝方坦克8", "hideTime": "2025/03/13 07:01:32"}, {"type": "model", "entityID": "蓝方导弹车0", "hideTime": "2025/03/13 07:01:32"}, {"type": "model", "entityID": "蓝方导弹车1", "hideTime": "2025/03/13 07:01:32"}, {"type": "model", "entityID": "蓝方导弹车2", "hideTime": "2025/03/13 07:01:32"}, {"type": "model", "entityID": "蓝方导弹车3", "hideTime": "2025/03/13 07:01:32"}, {"type": "model", "entityID": "蓝方导弹车4", "hideTime": "2025/03/13 07:01:32"}, {"type": "model", "entityID": "蓝方导弹车5", "hideTime": "2025/03/13 07:01:32"}, {"type": "model", "entityID": "蓝方导弹车6", "hideTime": "2025/03/13 07:01:32"}, {"type": "model", "entityID": "蓝方导弹车7", "hideTime": "2025/03/13 07:01:32"}, {"type": "model", "entityID": "蓝方导弹车8", "hideTime": "2025/03/13 07:01:32"}, {"type": "wall", "entityID": "蓝方坦克阵地", "hideTime": "2025/03/13 07:01:32"}, {"type": "wall", "entityID": "蓝方导弹阵地", "hideTime": "2025/03/13 07:01:32"}]}], "eventCommands": [{"title": "红蓝双方兵势概览", "description": "演习红蓝双方兵力部署及态势概览。", "currentTime": "2025/03/13 07:00:00", "duration": 2, "position": [119.507931, 24.174127, 684837.7774065954], "rotation": [28.66886080665072, -89.86610596530072, 0]}, {"title": "红方坦克阵地", "description": "", "currentTime": "2025/03/13 07:00:10", "duration": 2, "position": [116.896937, 24.990144, 12260.174795372259], "rotation": [59.18944600798979, -32.93973074449812, 359.99984625693617]}, {"title": "红方导弹阵地", "description": "", "currentTime": "2025/03/13 07:00:15", "duration": 2, "position": [117.729636, 25.604458, 13362.08722070717], "rotation": [53.47110541497954, -40.8413663888303, 359.999860118708]}, {"title": "红方地面站", "description": "", "currentTime": "2025/03/13 07:00:20", "duration": 2, "position": [118.141319, 24.57489, 9879.877325552787], "rotation": [79.15596281477723, -33.07028513063404, 0.0002675018192281407]}, {"title": "红方舰队及预警机编队", "description": "", "currentTime": "2025/03/13 07:00:25", "duration": 2, "position": [118.660821, 24.679517, 83640.78338160011], "rotation": [117.37345576954627, -52.04923103803873, 0.004891992364485755]}, {"title": "红方战斗机", "description": "", "currentTime": "2025/03/13 07:00:30", "duration": 2, "position": [119.512469, 24.63003, 7476.544068835714], "rotation": [32.922745119161874, -42.788168409815306, 359.9998877730763]}, {"title": "蓝方坦克阵地", "description": "", "currentTime": "2025/03/13 07:00:35", "duration": 2, "position": [120.720471, 23.70981, 12009.89341546675], "rotation": [334.8001624199467, -38.73771497821929, 0.000274787865341762]}, {"title": "蓝方导弹阵地", "description": "", "currentTime": "2025/03/13 07:00:40", "duration": 2, "position": [120.687443, 23.774358, 10199.716392804028], "rotation": [235.55355774552328, -37.39630182061778, 359.9993569360302]}, {"title": "蓝方地面站", "description": "", "currentTime": "2025/03/13 07:00:45", "duration": 2, "position": [120.480995, 24.132077, 595.8457666978597], "rotation": [287.65741465827136, -25.442594509519235, 6.007691317388048e-06]}, {"title": "蓝方战斗机", "description": "", "currentTime": "2025/03/13 07:00:50", "duration": 2, "position": [120.143683, 23.792911, 9167.658984147794], "rotation": [5.315701639968312, -47.53503216261066, 359.99996060790977]}, {"title": "红方舰队前进", "description": "", "currentTime": "2025/03/13 07:00:58", "duration": 2, "position": [118.724428, 24.623748, 49033.619319032136], "rotation": [118.29103991615453, -31.70538930045481, 0.0029710191442595306]}, {"title": "红方战斗机进攻", "description": "", "currentTime": "2025/03/13 07:01:18", "trackingID": "红方战斗机0", "headingOffset": 0, "pitchOffset": -20, "rangeHeight": 5000}, {"title": "发现蓝方战斗机, 并击毁", "description": "", "currentTime": "2025/03/13 07:01:22", "duration": 0, "position": [118.853661, 24.599485, 35247.23744443125], "rotation": [122.80797887362651, -23.368928913562254, 0.007553118521174801]}, {"title": "红方战斗机摧毁蓝方坦克阵地和导弹阵地", "description": "", "currentTime": "2025/03/13 07:01:26", "duration": 2, "position": [120.104126, 24.600693, 41797.03345746991], "rotation": [150.27508376368047, -31.35277052543992, 0.0023342184385795033]}, {"title": "红方胜利，推演结束", "description": "", "currentTime": "2025/03/13 07:01:35", "duration": 2, "position": [119.507931, 24.174127, 684837.7774065954], "rotation": [28.66886080665072, -89.86610596530072, 0]}]}