<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>远程控制功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .control-code {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border: 2px dashed #007bff;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.waiting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>远程控制功能测试</h1>
        
        <!-- 被控制端 -->
        <div class="section">
            <h3>被控制端（请求协助）</h3>
            <button id="requestAssistance">生成控制码</button>
            <button id="stopControl" disabled>停止控制</button>
            <div id="controlCodeDisplay" class="control-code" style="display: none;"></div>
            <div id="assistanceStatus" class="status disconnected">未连接</div>
        </div>
        
        <!-- 控制端 -->
        <div class="section">
            <h3>控制端（远程控制）</h3>
            <input type="text" id="controlCodeInput" placeholder="输入6位控制码" maxlength="6">
            <button id="connectControl">连接</button>
            <button id="disconnectControl" disabled>断开连接</button>
            <div id="controlStatus" class="status disconnected">未连接</div>
            
            <div style="margin-top: 15px;">
                <h4>控制命令</h4>
                <button id="zoomIn" disabled>放大地图</button>
                <button id="zoomOut" disabled>缩小地图</button>
                <button id="resetView" disabled>复位视角</button>
                <button id="resetNorth" disabled>指北针</button>
            </div>
        </div>
        
        <!-- 日志 -->
        <div class="section">
            <h3>连接日志</h3>
            <div id="log" class="log"></div>
            <button onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script>
        let assistanceWs = null;
        let controlWs = null;
        let currentControlCode = '';
        
        // 日志函数
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // 被控制端功能
        document.getElementById('requestAssistance').onclick = function() {
            const userId = 'test_user_' + Date.now();
            const wsUrl = `ws://localhost:8092/gis-services/remote-control/${userId}`;

            log(`被控制端用户ID: ${userId}`);
            log('正在连接WebSocket服务器...');
            assistanceWs = new WebSocket(wsUrl);
            
            assistanceWs.onopen = function() {
                log('WebSocket连接成功，请求生成控制码...');
                assistanceWs.send(JSON.stringify({
                    type: 'REQUEST_CONTROL_CODE'
                }));
            };
            
            assistanceWs.onmessage = function(event) {
                const message = JSON.parse(event.data);
                log(`被控制端收到消息: ${message.type} - ${message.message}`);
                log(`消息详情: ${JSON.stringify(message)}`);

                if (message.type === 'CONTROL_CODE_GENERATED') {
                    currentControlCode = message.data.controlCode;
                    document.getElementById('controlCodeDisplay').textContent = currentControlCode;
                    document.getElementById('controlCodeDisplay').style.display = 'block';
                    document.getElementById('assistanceStatus').textContent = '等待连接...';
                    document.getElementById('assistanceStatus').className = 'status waiting';
                    document.getElementById('stopControl').disabled = false;
                    log(`控制码生成成功: ${currentControlCode}`);
                } else if (message.type === 'CONTROLLER_CONNECTED') {
                    document.getElementById('assistanceStatus').textContent = '控制端已连接';
                    document.getElementById('assistanceStatus').className = 'status connected';
                    log(`控制端已连接，控制端用户ID: ${message.data.controllerUserId}`);
                } else if (message.type === 'CONTROL_DISCONNECTED') {
                    document.getElementById('assistanceStatus').textContent = '控制已断开';
                    document.getElementById('assistanceStatus').className = 'status disconnected';
                } else if (message.type === 'CONTROL_COMMAND') {
                    log(`执行控制命令: ${message.data.command}`);
                }
            };
            
            assistanceWs.onerror = function(error) {
                log('WebSocket错误: ' + error);
            };
            
            assistanceWs.onclose = function() {
                log('WebSocket连接关闭');
                document.getElementById('assistanceStatus').textContent = '连接已断开';
                document.getElementById('assistanceStatus').className = 'status disconnected';
            };
        };
        
        document.getElementById('stopControl').onclick = function() {
            if (assistanceWs) {
                assistanceWs.send(JSON.stringify({
                    type: 'DISCONNECT'
                }));
                assistanceWs.close();
                assistanceWs = null;
            }
            document.getElementById('controlCodeDisplay').style.display = 'none';
            document.getElementById('stopControl').disabled = true;
            document.getElementById('assistanceStatus').textContent = '未连接';
            document.getElementById('assistanceStatus').className = 'status disconnected';
        };
        
        // 控制端功能
        document.getElementById('connectControl').onclick = function() {
            const controlCode = document.getElementById('controlCodeInput').value;
            if (!controlCode || controlCode.length !== 6) {
                alert('请输入6位控制码');
                return;
            }

            const userId = 'controller_' + Date.now();
            const wsUrl = `ws://localhost:8092/gis-services/remote-control/${userId}`;

            log(`控制端用户ID: ${userId}`);
            log(`尝试连接控制码: ${controlCode}`);
            log('控制端正在连接WebSocket服务器...');
            controlWs = new WebSocket(wsUrl);
            
            controlWs.onopen = function() {
                log('控制端WebSocket连接成功，尝试连接控制码...');
                controlWs.send(JSON.stringify({
                    type: 'CONNECT_WITH_CODE',
                    controlCode: controlCode
                }));
            };
            
            controlWs.onmessage = function(event) {
                const message = JSON.parse(event.data);
                log(`控制端收到消息: ${message.type} - ${message.message}`);
                log(`控制端消息详情: ${JSON.stringify(message)}`);

                if (message.type === 'CONTROL_CONNECTED') {
                    document.getElementById('controlStatus').textContent = '远程控制已建立';
                    document.getElementById('controlStatus').className = 'status connected';
                    document.getElementById('disconnectControl').disabled = false;
                    document.getElementById('zoomIn').disabled = false;
                    document.getElementById('zoomOut').disabled = false;
                    document.getElementById('resetView').disabled = false;
                    document.getElementById('resetNorth').disabled = false;
                    log(`远程控制连接成功，被控制端用户ID: ${message.data.controlledUserId}`);
                } else if (message.type === 'CONTROL_DISCONNECTED') {
                    document.getElementById('controlStatus').textContent = '远程控制已断开';
                    document.getElementById('controlStatus').className = 'status disconnected';
                    resetControlButtons();
                }
            };
            
            controlWs.onerror = function(error) {
                log('控制端WebSocket错误: ' + error);
            };
            
            controlWs.onclose = function() {
                log('控制端WebSocket连接关闭');
                document.getElementById('controlStatus').textContent = '连接已断开';
                document.getElementById('controlStatus').className = 'status disconnected';
                resetControlButtons();
            };
        };
        
        document.getElementById('disconnectControl').onclick = function() {
            if (controlWs) {
                controlWs.send(JSON.stringify({
                    type: 'DISCONNECT'
                }));
                controlWs.close();
                controlWs = null;
            }
            resetControlButtons();
        };
        
        function resetControlButtons() {
            document.getElementById('disconnectControl').disabled = true;
            document.getElementById('zoomIn').disabled = true;
            document.getElementById('zoomOut').disabled = true;
            document.getElementById('resetView').disabled = true;
            document.getElementById('resetNorth').disabled = true;
        }
        
        // 控制命令
        document.getElementById('zoomIn').onclick = function() {
            sendControlCommand('ZOOM_IN');
        };
        
        document.getElementById('zoomOut').onclick = function() {
            sendControlCommand('ZOOM_OUT');
        };
        
        document.getElementById('resetView').onclick = function() {
            sendControlCommand('RESET_VIEW');
        };
        
        document.getElementById('resetNorth').onclick = function() {
            sendControlCommand('RESET_NORTH');
        };
        
        function sendControlCommand(command) {
            if (controlWs) {
                controlWs.send(JSON.stringify({
                    type: 'CONTROL_COMMAND',
                    data: {
                        command: command,
                        timestamp: Date.now()
                    }
                }));
                log(`发送控制命令: ${command}`);
            }
        }
        
        log('远程控制测试页面已加载');
    </script>
</body>
</html>
