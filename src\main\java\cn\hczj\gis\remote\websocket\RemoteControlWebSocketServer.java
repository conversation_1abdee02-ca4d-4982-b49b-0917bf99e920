package cn.hczj.gis.remote.websocket;

import cn.hczj.gis.remote.entity.RemoteControlSession;
import cn.hczj.gis.remote.service.RemoteControlService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 远程控制WebSocket服务端
 */
@ServerEndpoint("/remote-control/{userId}")
@Component
@Slf4j
public class RemoteControlWebSocketServer {
    
    private static RemoteControlService remoteControlService;
    
    /**
     * 存储用户ID到WebSocket会话的映射
     */
    private static final Map<String, RemoteControlWebSocketServer> userSessions = new ConcurrentHashMap<>();
    
    /**
     * 当前WebSocket会话
     */
    private Session session;
    
    /**
     * 当前用户ID
     */
    private String userId;
    
    @Autowired
    public void setRemoteControlService(RemoteControlService remoteControlService) {
        RemoteControlWebSocketServer.remoteControlService = remoteControlService;
    }
    
    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("userId") String userId) {
        this.session = session;
        this.userId = userId;
        
        userSessions.put(userId, this);
        log.info("远程控制WebSocket连接建立，用户ID: {}", userId);
        
        // 发送连接成功消息
        sendMessage(createMessage("CONNECT_SUCCESS", "连接成功", null));
    }
    
    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        if (userId != null) {
            userSessions.remove(userId);
            // 断开远程控制连接
            remoteControlService.disconnectByUserId(userId);
            log.info("远程控制WebSocket连接关闭，用户ID: {}", userId);
        }
    }
    
    /**
     * 收到客户端消息后调用的方法
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        try {
            JSONObject messageObj = JSON.parseObject(message);
            String type = messageObj.getString("type");
            
            switch (type) {
                case "REQUEST_CONTROL_CODE":
                    handleRequestControlCode();
                    break;
                case "CONNECT_WITH_CODE":
                    handleConnectWithCode(messageObj.getString("controlCode"));
                    break;
                case "DISCONNECT":
                    handleDisconnect();
                    break;
                case "CONTROL_COMMAND":
                    handleControlCommand(messageObj);
                    break;
                default:
                    log.warn("未知的消息类型: {}", type);
            }
        } catch (Exception e) {
            log.error("处理WebSocket消息失败", e);
            sendMessage(createMessage("ERROR", "消息处理失败: " + e.getMessage(), null));
        }
    }
    
    /**
     * 发生错误时调用
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("远程控制WebSocket发生错误，用户ID: {}", userId, error);
    }
    
    /**
     * 处理请求控制码
     */
    private void handleRequestControlCode() {
        try {
            String controlCode = remoteControlService.generateControlCode(userId);
            JSONObject data = new JSONObject();
            data.put("controlCode", controlCode);
            sendMessage(createMessage("CONTROL_CODE_GENERATED", "控制码生成成功", data));
        } catch (Exception e) {
            log.error("生成控制码失败", e);
            sendMessage(createMessage("ERROR", "生成控制码失败: " + e.getMessage(), null));
        }
    }
    
    /**
     * 处理通过控制码连接
     */
    private void handleConnectWithCode(String controlCode) {
        try {
            boolean success = remoteControlService.connectWithControlCode(controlCode, userId);
            if (success) {
                RemoteControlSession session = remoteControlService.getSession(controlCode);
                JSONObject data = new JSONObject();
                data.put("controlCode", controlCode);
                data.put("controlledUserId", session.getControlledUserId());
                
                sendMessage(createMessage("CONTROL_CONNECTED", "远程控制连接成功", data));
                
                // 通知被控制端
                RemoteControlWebSocketServer controlledSession = userSessions.get(session.getControlledUserId());
                if (controlledSession != null) {
                    JSONObject notifyData = new JSONObject();
                    notifyData.put("controllerUserId", userId);
                    notifyData.put("controlCode", controlCode);
                    controlledSession.sendMessage(createMessage("CONTROLLER_CONNECTED", "控制端已连接", notifyData));
                }
            } else {
                sendMessage(createMessage("ERROR", "控制码无效或已过期", null));
            }
        } catch (Exception e) {
            log.error("连接控制码失败", e);
            sendMessage(createMessage("ERROR", "连接失败: " + e.getMessage(), null));
        }
    }
    
    /**
     * 处理断开连接
     */
    private void handleDisconnect() {
        RemoteControlSession session = remoteControlService.getSessionByUserId(userId);
        if (session != null) {
            remoteControlService.disconnect(session.getControlCode());
            
            // 通知对方断开连接
            String otherUserId = userId.equals(session.getControlledUserId()) ? 
                session.getControllerUserId() : session.getControlledUserId();
            
            if (otherUserId != null) {
                RemoteControlWebSocketServer otherSession = userSessions.get(otherUserId);
                if (otherSession != null) {
                    otherSession.sendMessage(createMessage("CONTROL_DISCONNECTED", "远程控制已断开", null));
                }
            }
            
            sendMessage(createMessage("CONTROL_DISCONNECTED", "远程控制已断开", null));
        }
    }
    
    /**
     * 处理控制命令
     */
    private void handleControlCommand(JSONObject messageObj) {
        RemoteControlSession session = remoteControlService.getSessionByUserId(userId);
        if (session == null || !"CONNECTED".equals(session.getStatus())) {
            sendMessage(createMessage("ERROR", "未建立远程控制连接", null));
            return;
        }
        
        // 确保只有控制端可以发送命令
        if (!userId.equals(session.getControllerUserId())) {
            sendMessage(createMessage("ERROR", "只有控制端可以发送控制命令", null));
            return;
        }
        
        // 转发命令到被控制端
        RemoteControlWebSocketServer controlledSession = userSessions.get(session.getControlledUserId());
        if (controlledSession != null) {
            controlledSession.sendMessage(messageObj.toJSONString());
        } else {
            sendMessage(createMessage("ERROR", "被控制端已断开连接", null));
        }
    }
    
    /**
     * 发送消息
     */
    private void sendMessage(String message) {
        try {
            if (session != null && session.isOpen()) {
                session.getBasicRemote().sendText(message);
            }
        } catch (IOException e) {
            log.error("发送WebSocket消息失败", e);
        }
    }
    
    /**
     * 创建标准消息格式
     */
    private String createMessage(String type, String message, JSONObject data) {
        JSONObject messageObj = new JSONObject();
        messageObj.put("type", type);
        messageObj.put("message", message);
        messageObj.put("timestamp", System.currentTimeMillis());
        if (data != null) {
            messageObj.put("data", data);
        }
        return messageObj.toJSONString();
    }
}
