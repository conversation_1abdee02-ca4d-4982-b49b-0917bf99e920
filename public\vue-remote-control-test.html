<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue远程控制组件测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button-group {
            margin-bottom: 20px;
            text-align: center;
        }
        .button-group button {
            margin: 0 10px;
            padding: 10px 20px;
            font-size: 14px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: #409EFF;
            color: white;
        }
        .button-group button:hover {
            background: #66B1FF;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 4px;
            background: #f0f9ff;
            border: 1px solid #409EFF;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>Vue远程控制组件测试</h1>
            
            <div class="button-group">
                <button @click="testRequestAssistance">测试请求协助</button>
                <button @click="testRemoteControl">测试远程控制</button>
                <button @click="clearLog">清空日志</button>
            </div>
            
            <div class="status">
                <p><strong>状态：</strong>{{ status }}</p>
                <p><strong>当前控制码：</strong>{{ currentControlCode }}</p>
                <p><strong>连接状态：</strong>{{ connectionStatus }}</p>
            </div>
            
            <!-- 远程控制组件 -->
            <request-assistance 
                ref="requestAssistanceDialog" 
                v-if="showRequestAssistance" 
                @close="closeRequestAssistance" />
                
            <remote-control 
                ref="remoteControlDialog" 
                v-if="showRemoteControl" 
                @close="closeRemoteControl" />
            
            <!-- 日志区域 -->
            <div class="log" ref="logContainer">
                <div v-for="(log, index) in logs" :key="index">
                    [{{ log.time }}] {{ log.message }}
                </div>
            </div>
        </div>
    </div>

    <!-- Vue和Element UI -->
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    
    <script>
        // 模拟远程控制组件
        const RequestAssistance = {
            template: `
                <el-dialog
                    title="请求协助"
                    :visible.sync="visible"
                    width="400px"
                    @close="handleClose">
                    
                    <div v-if="status === 'generating'" style="text-align: center; padding: 20px;">
                        <i class="el-icon-loading" style="font-size: 24px; color: #409EFF;"></i>
                        <p>正在生成控制码...</p>
                    </div>
                    
                    <div v-else-if="status === 'waiting'" style="text-align: center;">
                        <h3>您的控制码</h3>
                        <div style="font-size: 36px; font-weight: bold; color: #409EFF; letter-spacing: 8px; margin: 20px 0; padding: 15px; border: 2px dashed #409EFF; border-radius: 8px; background: #f0f9ff;">
                            {{ controlCode }}
                        </div>
                        <p style="color: #606266;">请将此控制码告知协助人员</p>
                        <p style="color: #E6A23C; font-size: 12px;">控制码有效期：10分钟</p>
                        
                        <div style="display: flex; align-items: center; justify-content: center; margin-top: 20px;">
                            <i class="el-icon-time" style="color: #E6A23C; margin-right: 8px;"></i>
                            <span>等待协助人员连接...</span>
                        </div>
                    </div>
                    
                    <div v-else-if="status === 'connected'" style="text-align: center;">
                        <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 15px;">
                            <i class="el-icon-success" style="color: #67C23A; margin-right: 8px; font-size: 16px;"></i>
                            <span>协助人员已连接</span>
                        </div>
                        <div style="background: #f0f9ff; padding: 15px; border-radius: 8px;">
                            <p style="margin: 5px 0; color: #606266;">控制端用户：{{ controllerUserId }}</p>
                            <p style="margin: 5px 0; color: #606266;">连接时间：{{ connectTime }}</p>
                        </div>
                    </div>
                    
                    <div v-else-if="status === 'error'" style="text-align: center; padding: 20px;">
                        <i class="el-icon-error" style="font-size: 24px; color: #F56C6C; margin-bottom: 10px;"></i>
                        <p style="color: #F56C6C;">{{ errorMessage }}</p>
                    </div>
                    
                    <div slot="footer" class="dialog-footer">
                        <el-button v-if="status === 'waiting' || status === 'connected'" type="danger" @click="stopControl">停止控制</el-button>
                        <el-button @click="handleClose">关闭</el-button>
                    </div>
                </el-dialog>
            `,
            data() {
                return {
                    visible: false,
                    status: 'generating',
                    controlCode: '',
                    controllerUserId: '',
                    connectTime: '',
                    errorMessage: '',
                    websocket: null,
                }
            },
            methods: {
                show() {
                    this.visible = true
                    this.status = 'generating'
                    this.controlCode = ''
                    this.controllerUserId = ''
                    this.connectTime = ''
                    this.errorMessage = ''
                    this.connectWebSocket()
                },
                
                hide() {
                    this.visible = false
                    this.disconnectWebSocket()
                },
                
                handleClose() {
                    this.hide()
                    this.$emit('close')
                },
                
                stopControl() {
                    if (this.websocket) {
                        this.websocket.send(JSON.stringify({
                            type: 'DISCONNECT'
                        }))
                    }
                    this.status = 'generating'
                    this.controllerUserId = ''
                    this.connectTime = ''
                },
                
                connectWebSocket() {
                    const userId = 'test_user_' + Date.now()
                    const wsUrl = `ws://localhost:8092/gis-services/remote-control/${userId}`
                    
                    this.$parent.addLog(`连接WebSocket: ${wsUrl}`)
                    
                    try {
                        this.websocket = new WebSocket(wsUrl)
                        
                        this.websocket.onopen = () => {
                            this.$parent.addLog('WebSocket连接成功')
                            this.websocket.send(JSON.stringify({
                                type: 'REQUEST_CONTROL_CODE'
                            }))
                        }
                        
                        this.websocket.onmessage = (event) => {
                            this.handleWebSocketMessage(JSON.parse(event.data))
                        }
                        
                        this.websocket.onerror = (error) => {
                            this.$parent.addLog('WebSocket错误: ' + error)
                            this.status = 'error'
                            this.errorMessage = '连接服务器失败'
                        }
                        
                        this.websocket.onclose = () => {
                            this.$parent.addLog('WebSocket连接关闭')
                        }
                    } catch (error) {
                        this.$parent.addLog('创建WebSocket失败: ' + error)
                        this.status = 'error'
                        this.errorMessage = '无法连接到服务器'
                    }
                },
                
                disconnectWebSocket() {
                    if (this.websocket) {
                        this.websocket.close()
                        this.websocket = null
                    }
                },
                
                handleWebSocketMessage(message) {
                    this.$parent.addLog(`收到消息: ${message.type} - ${message.message}`)
                    
                    switch (message.type) {
                        case 'CONTROL_CODE_GENERATED':
                            this.controlCode = message.data.controlCode
                            this.status = 'waiting'
                            this.$parent.currentControlCode = this.controlCode
                            break
                            
                        case 'CONTROLLER_CONNECTED':
                            this.controllerUserId = message.data.controllerUserId
                            this.connectTime = new Date().toLocaleString()
                            this.status = 'connected'
                            this.$message.success('协助人员已连接')
                            break
                            
                        case 'CONTROL_DISCONNECTED':
                            this.status = 'waiting'
                            this.controllerUserId = ''
                            this.connectTime = ''
                            this.$message.info('远程控制已断开')
                            break
                            
                        case 'ERROR':
                            this.status = 'error'
                            this.errorMessage = message.message
                            this.$message.error(message.message)
                            break
                    }
                }
            },
            
            beforeDestroy() {
                this.disconnectWebSocket()
            }
        }
        
        const RemoteControl = {
            template: `
                <el-dialog
                    title="远程控制"
                    :visible.sync="visible"
                    width="450px"
                    @close="handleClose">
                    
                    <div v-if="status === 'input'">
                        <h3 style="text-align: center;">请输入控制码</h3>
                        <el-input
                            v-model="inputControlCode"
                            placeholder="请输入6位数字控制码"
                            maxlength="6"
                            show-word-limit
                            size="large"
                            style="margin-bottom: 15px;"
                            @keyup.enter.native="connectToTarget">
                            <template slot="prepend">控制码</template>
                        </el-input>
                        <p style="color: #909399; font-size: 12px; text-align: center;">请向被控制方获取6位数字控制码</p>
                    </div>
                    
                    <div v-else-if="status === 'connecting'" style="text-align: center; padding: 20px;">
                        <i class="el-icon-loading" style="font-size: 24px; color: #409EFF;"></i>
                        <p>正在连接到目标设备...</p>
                    </div>
                    
                    <div v-else-if="status === 'connected'">
                        <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 20px;">
                            <i class="el-icon-success" style="color: #67C23A; margin-right: 8px; font-size: 16px;"></i>
                            <span>远程控制已建立</span>
                        </div>
                        
                        <div style="background: #f0f9ff; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                            <p style="margin: 5px 0; color: #606266;">目标用户：{{ controlledUserId }}</p>
                            <p style="margin: 5px 0; color: #606266;">连接时间：{{ connectTime }}</p>
                            <p style="margin: 5px 0; color: #606266;">控制码：{{ currentControlCode }}</p>
                        </div>
                        
                        <div>
                            <h4>控制面板</h4>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                                <el-button type="primary" icon="el-icon-zoom-in" @click="sendControlCommand('ZOOM_IN')" size="small">放大地图</el-button>
                                <el-button type="primary" icon="el-icon-zoom-out" @click="sendControlCommand('ZOOM_OUT')" size="small">缩小地图</el-button>
                                <el-button type="primary" icon="el-icon-refresh" @click="sendControlCommand('RESET_VIEW')" size="small">复位视角</el-button>
                                <el-button type="primary" icon="el-icon-location" @click="sendControlCommand('RESET_NORTH')" size="small">指北针</el-button>
                            </div>
                        </div>
                    </div>
                    
                    <div v-else-if="status === 'error'" style="text-align: center; padding: 20px;">
                        <i class="el-icon-error" style="font-size: 24px; color: #F56C6C; margin-bottom: 10px;"></i>
                        <p style="color: #F56C6C;">{{ errorMessage }}</p>
                    </div>
                    
                    <div slot="footer" class="dialog-footer">
                        <el-button v-if="status === 'input'" type="primary" @click="connectToTarget" :disabled="!inputControlCode || inputControlCode.length !== 6">连接</el-button>
                        <el-button v-if="status === 'connected'" type="danger" @click="disconnectControl">断开控制</el-button>
                        <el-button @click="handleClose">关闭</el-button>
                    </div>
                </el-dialog>
            `,
            data() {
                return {
                    visible: false,
                    status: 'input',
                    inputControlCode: '',
                    currentControlCode: '',
                    controlledUserId: '',
                    connectTime: '',
                    errorMessage: '',
                    websocket: null,
                }
            },
            methods: {
                show() {
                    this.visible = true
                    this.status = 'input'
                    this.inputControlCode = ''
                    this.currentControlCode = ''
                    this.controlledUserId = ''
                    this.connectTime = ''
                    this.errorMessage = ''
                    this.connectWebSocket()
                },
                
                hide() {
                    this.visible = false
                    this.disconnectWebSocket()
                },
                
                handleClose() {
                    this.hide()
                    this.$emit('close')
                },
                
                connectToTarget() {
                    if (!this.inputControlCode || this.inputControlCode.length !== 6) {
                        this.$message.warning('请输入6位数字控制码')
                        return
                    }
                    
                    this.status = 'connecting'
                    
                    if (this.websocket) {
                        this.websocket.send(JSON.stringify({
                            type: 'CONNECT_WITH_CODE',
                            controlCode: this.inputControlCode
                        }))
                    }
                },
                
                disconnectControl() {
                    if (this.websocket) {
                        this.websocket.send(JSON.stringify({
                            type: 'DISCONNECT'
                        }))
                    }
                    this.status = 'input'
                    this.currentControlCode = ''
                    this.controlledUserId = ''
                    this.connectTime = ''
                },
                
                sendControlCommand(command) {
                    if (this.websocket && this.status === 'connected') {
                        this.websocket.send(JSON.stringify({
                            type: 'CONTROL_COMMAND',
                            data: {
                                command: command,
                                timestamp: Date.now()
                            }
                        }))
                        
                        this.$message.success(`已发送${this.getCommandName(command)}命令`)
                        this.$parent.addLog(`发送控制命令: ${command}`)
                    }
                },
                
                getCommandName(command) {
                    const commandNames = {
                        'ZOOM_IN': '放大地图',
                        'ZOOM_OUT': '缩小地图',
                        'RESET_VIEW': '复位视角',
                        'RESET_NORTH': '指北针'
                    }
                    return commandNames[command] || command
                },
                
                connectWebSocket() {
                    const userId = 'controller_' + Date.now()
                    const wsUrl = `ws://localhost:8092/gis-services/remote-control/${userId}`
                    
                    this.$parent.addLog(`控制端连接WebSocket: ${wsUrl}`)
                    
                    try {
                        this.websocket = new WebSocket(wsUrl)
                        
                        this.websocket.onopen = () => {
                            this.$parent.addLog('控制端WebSocket连接成功')
                        }
                        
                        this.websocket.onmessage = (event) => {
                            this.handleWebSocketMessage(JSON.parse(event.data))
                        }
                        
                        this.websocket.onerror = (error) => {
                            this.$parent.addLog('控制端WebSocket错误: ' + error)
                            this.status = 'error'
                            this.errorMessage = '连接服务器失败'
                        }
                        
                        this.websocket.onclose = () => {
                            this.$parent.addLog('控制端WebSocket连接关闭')
                        }
                    } catch (error) {
                        this.$parent.addLog('控制端创建WebSocket失败: ' + error)
                        this.status = 'error'
                        this.errorMessage = '无法连接到服务器'
                    }
                },
                
                disconnectWebSocket() {
                    if (this.websocket) {
                        this.websocket.close()
                        this.websocket = null
                    }
                },
                
                handleWebSocketMessage(message) {
                    this.$parent.addLog(`控制端收到消息: ${message.type} - ${message.message}`)
                    
                    switch (message.type) {
                        case 'CONTROL_CONNECTED':
                            this.currentControlCode = message.data.controlCode
                            this.controlledUserId = message.data.controlledUserId
                            this.connectTime = new Date().toLocaleString()
                            this.status = 'connected'
                            this.$message.success('远程控制连接成功')
                            break
                            
                        case 'CONTROL_DISCONNECTED':
                            this.status = 'input'
                            this.currentControlCode = ''
                            this.controlledUserId = ''
                            this.connectTime = ''
                            this.$message.info('远程控制已断开')
                            break
                            
                        case 'ERROR':
                            this.status = 'error'
                            this.errorMessage = message.message
                            this.$message.error(message.message)
                            break
                    }
                }
            },
            
            beforeDestroy() {
                this.disconnectWebSocket()
            }
        }
        
        // Vue应用
        new Vue({
            el: '#app',
            components: {
                RequestAssistance,
                RemoteControl
            },
            data() {
                return {
                    status: '未连接',
                    currentControlCode: '',
                    connectionStatus: '断开',
                    showRequestAssistance: false,
                    showRemoteControl: false,
                    logs: []
                }
            },
            methods: {
                testRequestAssistance() {
                    this.showRequestAssistance = true
                    this.$nextTick(() => {
                        this.$refs.requestAssistanceDialog.show()
                    })
                },
                
                testRemoteControl() {
                    this.showRemoteControl = true
                    this.$nextTick(() => {
                        this.$refs.remoteControlDialog.show()
                    })
                },
                
                closeRequestAssistance() {
                    this.showRequestAssistance = false
                },
                
                closeRemoteControl() {
                    this.showRemoteControl = false
                },
                
                addLog(message) {
                    this.logs.push({
                        time: new Date().toLocaleTimeString(),
                        message: message
                    })
                    
                    this.$nextTick(() => {
                        const logContainer = this.$refs.logContainer
                        logContainer.scrollTop = logContainer.scrollHeight
                    })
                },
                
                clearLog() {
                    this.logs = []
                }
            }
        })
    </script>
</body>
</html>
