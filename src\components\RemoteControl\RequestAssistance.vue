<template>
    <el-dialog
        title="请求协助"
        :visible.sync="visible"
        width="400px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="handleClose"
        custom-class="request-assistance-dialog">

        <!-- 等待生成控制码状态 -->
        <div v-if="status === 'generating'" class="status-container">
            <i class="el-icon-loading loading-icon"></i>
            <p>正在生成控制码...</p>
        </div>

        <!-- 显示控制码状态 -->
        <div v-else-if="status === 'waiting'" class="control-code-container">
            <div class="code-display">
                <h3>您的控制码</h3>
                <div class="control-code">{{ controlCode }}</div>
                <p class="code-tip">请将此控制码告知协助人员</p>
                <p class="expire-tip">控制码有效期：10分钟</p>
            </div>

            <div class="status-info">
                <i class="el-icon-time status-icon waiting"></i>
                <span>等待协助人员连接...</span>
            </div>
        </div>

        <!-- 已连接状态 -->
        <div v-else-if="status === 'connected'" class="connected-container">
            <div class="status-info">
                <i class="el-icon-success status-icon connected"></i>
                <span>协助人员已连接</span>
            </div>
            <div class="controller-info">
                <p>控制端用户：{{ controllerUserId }}</p>
                <p>连接时间：{{ connectTime }}</p>
            </div>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="status === 'error'" class="error-container">
            <i class="el-icon-error status-icon error"></i>
            <p>{{ errorMessage }}</p>
        </div>

        <!-- 调试信息 -->
        <div style="margin-top: 10px; padding: 10px; background: #f5f5f5; border-radius: 4px; font-size: 12px;">
            <p>调试信息：</p>
            <p>当前状态：{{ status }}</p>
            <p>控制码：{{ controlCode }}</p>
            <p>控制端用户：{{ controllerUserId }}</p>
            <p>连接时间：{{ connectTime }}</p>
        </div>

        <div slot="footer" class="dialog-footer">
            <el-button
                v-if="status === 'waiting' || status === 'connected'"
                type="danger"
                @click="stopControl">
                停止控制
            </el-button>
            <el-button @click="handleClose">关闭</el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    name: 'RequestAssistance',
    data() {
        return {
            visible: false,
            status: 'generating', // generating, waiting, connected, error
            controlCode: '',
            controllerUserId: '',
            connectTime: '',
            errorMessage: '',
            websocket: null,
        }
    },
    methods: {
        show() {
            this.visible = true
            this.status = 'generating'
            this.controlCode = ''
            this.controllerUserId = ''
            this.connectTime = ''
            this.errorMessage = ''
            this.connectWebSocket()
        },

        hide() {
            this.visible = false
            this.disconnectWebSocket()
        },

        handleClose() {
            this.hide()
            this.$emit('close')
        },

        stopControl() {
            if (this.websocket) {
                this.websocket.send(JSON.stringify({
                    type: 'DISCONNECT'
                }))
            }
            this.status = 'generating'
            this.controllerUserId = ''
            this.connectTime = ''
        },

        connectWebSocket() {
            // 获取当前用户ID（这里需要根据实际情况获取）
            const userId = this.$store.getters['auth/user']?.id || 'user_' + Date.now()
            // 由于这是纯前端项目，我们使用模拟的WebSocket URL
            const wsUrl = `ws://localhost:8092/gis-services/remote-control/${userId}`

            try {
                this.websocket = new WebSocket(wsUrl)

                this.websocket.onopen = () => {
                    console.log('远程控制WebSocket连接成功')
                    // 请求生成控制码
                    this.websocket.send(JSON.stringify({
                        type: 'REQUEST_CONTROL_CODE'
                    }))
                }

                this.websocket.onmessage = (event) => {
                    this.handleWebSocketMessage(JSON.parse(event.data))
                }

                this.websocket.onerror = (error) => {
                    console.error('远程控制WebSocket错误:', error)
                    this.status = 'error'
                    this.errorMessage = '连接服务器失败'
                }

                this.websocket.onclose = () => {
                    console.log('远程控制WebSocket连接关闭')
                }
            } catch (error) {
                console.error('创建WebSocket连接失败:', error)
                this.status = 'error'
                this.errorMessage = '无法连接到服务器'
            }
        },

        disconnectWebSocket() {
            if (this.websocket) {
                this.websocket.close()
                this.websocket = null
            }
        },

        handleWebSocketMessage(message) {
            console.log('被控制端收到WebSocket消息:', message)
            console.log('当前状态:', this.status)

            switch (message.type) {
                case 'CONTROL_CODE_GENERATED':
                    console.log('生成控制码:', message.data.controlCode)
                    this.controlCode = message.data.controlCode
                    this.status = 'waiting'
                    console.log('状态更新为waiting')
                    break

                case 'CONTROLLER_CONNECTED':
                    console.log('控制端连接，数据:', message.data)
                    this.controllerUserId = message.data.controllerUserId
                    this.connectTime = new Date().toLocaleString()
                    this.status = 'connected'
                    console.log('状态更新为connected，控制端用户:', this.controllerUserId)
                    this.$message.success('协助人员已连接')
                    // 使用nextTick确保DOM更新
                    this.$nextTick(() => {
                        console.log('DOM更新完成，当前状态:', this.status)
                    })
                    break

                case 'CONTROL_DISCONNECTED':
                    this.status = 'waiting'
                    this.controllerUserId = ''
                    this.connectTime = ''
                    this.$message.info('远程控制已断开')
                    break

                case 'CONTROL_COMMAND':
                    this.handleControlCommand(message)
                    break

                case 'ERROR':
                    this.status = 'error'
                    this.errorMessage = message.message
                    this.$message.error(message.message)
                    break
            }
        },

        handleControlCommand(message) {
            // 处理控制命令
            console.log('收到控制命令:', message)

            if (message.data && message.data.command) {
                // 通过EventBus发送控制命令
                this.$bus.$emit('remote-control-command', message.data)
            }
        }
    },

    beforeDestroy() {
        this.disconnectWebSocket()
    }
}
</script>

<style lang="scss" scoped>
.request-assistance-dialog {
    .status-container {
        text-align: center;
        padding: 20px;

        .loading-icon {
            font-size: 24px;
            color: #409EFF;
            margin-bottom: 10px;
        }
    }

    .control-code-container {
        text-align: center;

        .code-display {
            margin-bottom: 20px;

            h3 {
                margin-bottom: 15px;
                color: #303133;
            }

            .control-code {
                font-size: 36px;
                font-weight: bold;
                color: #409EFF;
                letter-spacing: 8px;
                margin: 20px 0;
                padding: 15px;
                border: 2px dashed #409EFF;
                border-radius: 8px;
                background: #f0f9ff;
            }

            .code-tip {
                color: #606266;
                margin-bottom: 5px;
            }

            .expire-tip {
                color: #E6A23C;
                font-size: 12px;
            }
        }

        .status-info {
            display: flex;
            align-items: center;
            justify-content: center;

            .status-icon {
                margin-right: 8px;
                font-size: 16px;

                &.waiting {
                    color: #E6A23C;
                }

                &.connected {
                    color: #67C23A;
                }

                &.error {
                    color: #F56C6C;
                }
            }
        }
    }

    .connected-container {
        text-align: center;

        .status-info {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;

            .status-icon.connected {
                color: #67C23A;
                margin-right: 8px;
                font-size: 16px;
            }
        }

        .controller-info {
            background: #f0f9ff;
            padding: 15px;
            border-radius: 8px;

            p {
                margin: 5px 0;
                color: #606266;
            }
        }
    }

    .error-container {
        text-align: center;
        padding: 20px;

        .status-icon.error {
            font-size: 24px;
            color: #F56C6C;
            margin-bottom: 10px;
        }

        p {
            color: #F56C6C;
        }
    }
}
</style>
