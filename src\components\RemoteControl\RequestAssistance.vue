<template>
    <el-dialog
        title="请求协助"
        :visible.sync="visible"
        width="400px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="handleClose"
        custom-class="request-assistance-dialog">

        <!-- 等待生成控制码状态 -->
        <div v-if="status === 'generating'" class="status-container">
            <i class="el-icon-loading loading-icon"></i>
            <p>正在生成控制码...</p>
        </div>

        <!-- 显示控制码状态 -->
        <div v-else-if="status === 'waiting'" class="control-code-container">
            <div class="code-display">
                <h3>您的控制码</h3>
                <div class="control-code">{{ controlCode }}</div>
                <p class="code-tip">请将此控制码告知协助人员</p>
                <p class="expire-tip">控制码有效期：10分钟</p>
            </div>

            <div class="status-info">
                <i class="el-icon-time status-icon waiting"></i>
                <span>等待协助人员连接...</span>
            </div>
        </div>

        <!-- 已连接状态 -->
        <div v-else-if="status === 'connected'" class="connected-container">
            <div class="status-info">
                <i class="el-icon-success status-icon connected"></i>
                <span>协助人员已连接</span>
            </div>
            <div class="controller-info">
                <p>控制端用户：{{ controllerUserId }}</p>
                <p>连接时间：{{ connectTime }}</p>
            </div>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="status === 'error'" class="error-container">
            <i class="el-icon-error status-icon error"></i>
            <p>{{ errorMessage }}</p>
        </div>



        <div slot="footer" class="dialog-footer">
            <el-button
                v-if="status === 'waiting' || status === 'connected'"
                type="danger"
                @click="stopControl">
                停止控制
            </el-button>
            <el-button @click="handleClose">关闭</el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    name: 'RequestAssistance',
    data() {
        return {
            visible: false,
            status: 'generating', // generating, waiting, connected, error
            controlCode: '',
            controllerUserId: '',
            connectTime: '',
            errorMessage: '',
            websocket: null,
            userId: '',
            statusPollingTimer: null,
        }
    },
    methods: {
        show() {
            this.visible = true
            this.status = 'generating'
            this.controlCode = ''
            this.controllerUserId = ''
            this.connectTime = ''
            this.errorMessage = ''
            this.connectWebSocket()
        },

        hide() {
            this.visible = false
            this.disconnectWebSocket()
        },

        handleClose() {
            this.hide()
            this.$emit('close')
        },

        stopControl() {
            if (this.websocket) {
                this.websocket.send(JSON.stringify({
                    type: 'DISCONNECT'
                }))
            }
            this.status = 'generating'
            this.controllerUserId = ''
            this.connectTime = ''
        },

        async connectWebSocket() {
            // 获取当前用户ID（这里需要根据实际情况获取）
            const userId = this.$store.getters['auth/user']?.id || 'user_' + Date.now()

            try {
                // 使用HTTP API请求控制码
                const response = await fetch('http://localhost:8093/api/remote-control/request-code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ userId })
                })

                const result = await response.json()

                if (result.success) {
                    console.log('控制码生成成功:', result.data.controlCode)
                    this.controlCode = result.data.controlCode
                    this.status = 'waiting'
                    this.userId = result.data.userId

                    // 开始轮询检查连接状态
                    this.startStatusPolling()
                } else {
                    console.error('生成控制码失败:', result.message)
                    this.status = 'error'
                    this.errorMessage = result.message
                }
            } catch (error) {
                console.error('请求控制码失败:', error)
                this.status = 'error'
                this.errorMessage = '无法连接到服务器'
            }
        },

        disconnectWebSocket() {
            // 停止状态轮询
            this.stopStatusPolling()

            // 如果有控制码，发送断开连接请求
            if (this.controlCode) {
                this.sendDisconnectRequest()
            }
        },

        startStatusPolling() {
            // 每2秒检查一次连接状态
            this.statusPollingTimer = setInterval(async () => {
                if (this.controlCode && this.status === 'waiting') {
                    await this.checkConnectionStatus()
                }
            }, 2000)
        },

        stopStatusPolling() {
            if (this.statusPollingTimer) {
                clearInterval(this.statusPollingTimer)
                this.statusPollingTimer = null
            }
        },

        async checkConnectionStatus() {
            try {
                const response = await fetch(`http://localhost:8093/api/remote-control/status?controlCode=${this.controlCode}`)
                const result = await response.json()

                if (result.success && result.data) {
                    const sessionData = result.data

                    if (sessionData.status === 'CONNECTED' && this.status !== 'connected') {
                        // 状态变为已连接
                        this.controllerUserId = sessionData.controllerUserId
                        this.connectTime = new Date(sessionData.connectTime).toLocaleString()
                        this.status = 'connected'
                        this.$message.success('协助人员已连接')
                        console.log('状态更新为connected，控制端用户:', this.controllerUserId)

                        // 停止轮询
                        this.stopStatusPolling()
                    }
                }
            } catch (error) {
                console.error('检查连接状态失败:', error)
            }
        },

        async sendDisconnectRequest() {
            try {
                await fetch('http://localhost:8093/api/remote-control/disconnect', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ controlCode: this.controlCode })
                })
            } catch (error) {
                console.error('发送断开连接请求失败:', error)
            }
        },

        handleControlCommand(command) {
            // 处理控制命令
            console.log('收到控制命令:', command)

            if (command) {
                // 通过EventBus发送控制命令
                this.$bus.$emit('remote-control-command', command)
            }
        }
    },

    beforeDestroy() {
        this.disconnectWebSocket()
        this.stopStatusPolling()
    }
}
</script>

<style lang="scss" scoped>
.request-assistance-dialog {
    .status-container {
        text-align: center;
        padding: 20px;

        .loading-icon {
            font-size: 24px;
            color: #409EFF;
            margin-bottom: 10px;
        }
    }

    .control-code-container {
        text-align: center;

        .code-display {
            margin-bottom: 20px;

            h3 {
                margin-bottom: 15px;
                color: #303133;
            }

            .control-code {
                font-size: 36px;
                font-weight: bold;
                color: #409EFF;
                letter-spacing: 8px;
                margin: 20px 0;
                padding: 15px;
                border: 2px dashed #409EFF;
                border-radius: 8px;
                background: #f0f9ff;
            }

            .code-tip {
                color: #606266;
                margin-bottom: 5px;
            }

            .expire-tip {
                color: #E6A23C;
                font-size: 12px;
            }
        }

        .status-info {
            display: flex;
            align-items: center;
            justify-content: center;

            .status-icon {
                margin-right: 8px;
                font-size: 16px;

                &.waiting {
                    color: #E6A23C;
                }

                &.connected {
                    color: #67C23A;
                }

                &.error {
                    color: #F56C6C;
                }
            }
        }
    }

    .connected-container {
        text-align: center;

        .status-info {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;

            .status-icon.connected {
                color: #67C23A;
                margin-right: 8px;
                font-size: 16px;
            }
        }

        .controller-info {
            background: #f0f9ff;
            padding: 15px;
            border-radius: 8px;

            p {
                margin: 5px 0;
                color: #606266;
            }
        }
    }

    .error-container {
        text-align: center;
        padding: 20px;

        .status-icon.error {
            font-size: 24px;
            color: #F56C6C;
            margin-bottom: 10px;
        }

        p {
            color: #F56C6C;
        }
    }
}
</style>
