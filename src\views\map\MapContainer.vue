<template>
    <div id="map-container">
        <div class="maskBg"></div>

        <!--     上下左右四个角的图标       -->
        <!-- <div class="triangle-lt"></div>
		<div class="triangle-lb"></div>
		<div class="triangle-rt"></div>
		<div class="triangle-rb"></div> -->

        <!-- <Map3D ref="Map3D" v-show="mapMode === '3D' || mapMode === '23D'" />
        <Map2D ref="Map2D" v-show="mapMode === '2D' || mapMode === '23D'" />-->

        <Map3D ref="Map3D" :class="mapMode === '2D' ? 'invisible' : ''" />
        <Map2D ref="Map2D" :class="mapMode === '3D' ? 'invisible' : ''" />

        <div id="profile-analysis"></div>

        <SynergyPlot />
        <DeductionController v-show="situationDeductionVisible" />

        <RangeSearch v-if="rangeSearchVisible" />
        <ListButton />
        <tabTour></tabTour>


    </div>
</template>

<script>
import Map3D from "@/views/map/Map3D";
import Map2D from "@/views/map/Map2D";
import SynergyPlot from "@/components/map/synergy/index";
import DeductionController from "@/components/map/deduction/DeductionController";

import Graticule from '@/components/map/3d/nav/Graticule.js'

import Popups3D from "./Popups3D";
import Popups2D from "./Popups2D";
import { mapGetters, mapMutations, mapState } from "vuex";
import PointMilitaryMarkInputer3D from "@/military/3d/inputer/PointMilitaryMarkInputer3D";
import PolylineMilitaryMarkInputer3D from "@/military/3d/inputer/PolylineMilitaryMarkInputer3D";
import PolygonMilitaryMarkInputer3D from "@/military/3d/inputer/PolygonMilitaryMarkInputer3D";

import PointMilitaryMarkInputer2D from "@/military/2d/inputer/PointMilitaryMarkInputer2D";
import PolylineMilitaryMarkInputer2D from "@/military/2d/inputer/PolylineMilitaryMarkInputer2D";
import PolygonMilitaryMarkInputer2D from "@/military/2d/inputer/PolygonMilitaryMarkInputer2D";

import PointMilitaryMarkEditer3D from "@/military/3d/editer/PointMilitaryMarkEditer3D";
import PointMilitaryMarkEditer2D from "@/military/2d/editer/PointMilitaryMarkEditer2D";

import NotPointMilitaryMarkEditer3D from "@/military/3d/editer/NotPointMilitaryMarkEditer3D";
import NotPointMilitaryMarkEditer2D from "@/military/2d/editer/NotPointMilitaryMarkEditer2D";

import LinkageWebSocketClient from "@/views/target/LinkageWebSocketClient"

import { isArray, forEach, map } from "lodash";
import { near } from "@/api/common";
import MapLocation from "@/mixins/map-location";
import dialog from "@/mixins/dialog";
import RangeSearch from "@/components/RangeSearch";
import ListButton from "@/components/ListButton";
import tabTour from "@/views/tabTour"

import { zoomIn, zoomOut } from "./InstructEvent.js"
import { RemoteControlCommandManager } from "@/utils/RemoteControlCommands"
import remoteControlWebSocket from "@/utils/RemoteControlWebSocket"

let timer = null

window.goalsArray = new Cesium.AssociativeArray()
window.equipArray = new Cesium.AssociativeArray()
window.zyequipArray = new Cesium.AssociativeArray()
window.teamArray = new Cesium.AssociativeArray()

export default {
    components: {
        Map3D,
        Map2D,
        SynergyPlot,
        DeductionController,
        RangeSearch,
        ListButton,
        tabTour,
    },

    mixins: [MapLocation, dialog],
    data() {
        return {
            isFullScreen: false,
            fullscreenBtnStr: "全屏显示",
            mapModeList: [
                {
                    label: "二维",
                    value: "2D",
                },
                {
                    label: "三维",
                    value: "3D",
                },
                {
                    label: "二三维",
                    value: "23D",
                },
            ],
        };
    },
    computed: {
        ...mapGetters("map", ["mapMode", "rangeSearchVisible"]),
        ...mapGetters("synergyPlot", ["synergyGroupTableVisible"]),
        ...mapGetters("situationDeduction", ["situationDeductionVisible"]),
        ...mapState("samplesModule", [
            "flattenActived",
        ]),
        ...mapState("layerLoad", ["centerLocationVisible", 'changeMenu']),
    },
    watch: {},
    methods: {
        ...mapMutations("map", ["changeMapMode"]),
        ...mapMutations('commonModule', ['changeActiveMarkContainer']),
        ...mapMutations("layerLoad", ['changeMenuVisible']),
        showNavBtn() {
            this.$refs["Map3D"] && this.$refs["Map3D"].showNavBtn();
            this.$refs["Map2D"] && this.$refs["Map2D"].showNavBtn();
        },
        handlerFullscreenEvent() {
            if (this.isFullScreen) {
                this.isFullScreen = false;
                Cesium.Fullscreen.exitFullscreen();
                this.fullscreenBtnStr = "全屏显示";
            } else {
                this.isFullScreen = true;
                Cesium.Fullscreen.requestFullscreen(document.body);
                this.fullscreenBtnStr = "退出全屏";
            }
        },
        initMap2D() {
            window.Free2DMap.sceneUtil.defalutZoomLevel = 12;
            window.Free2DMap.sceneUtil.defalutZoomCenter = ol.proj.transform([INIT_VIEWPORT[0], INIT_VIEWPORT[1]], "EPSG:4326", "EPSG:3857")
            // console.log(ol.proj.transform([113.826942, 34.022787], "EPSG:4326", "EPSG:3857"))
        },
        initMap3D() {
            window.FreeEarth.navigator.setHomeView({
                destination: INIT_VIEWPORT,
                rotation: INIT_ROTATION,
                duration: INIT_DURATION,
            });
        },
        changeViewSync() {
            // 默认视点是否同步
            let isViewSyncOpen =
                window.FreeIntegrated.getSynchronizeViewState();
            FreeIntegrated.setSynchronizeViewState(!isViewSyncOpen);
            this.$emit("onChangeViewSync", !isViewSyncOpen);
        },

        // mark-------------------------begin
        createMark(params) {
            const id = Cesium.createGuid();
            params.id = id;
            this.createMark2D(params);
            this.createMark3D(params);
        },
        createMark2D(params) {
            const fePoint = new FreeXMap.FePoint({
                id: params.id,
                positions: params.position,
                name: params.name,
                image: params.image,
                imageSize: 64,
                mapState: Free2DMap.getMapState(),
            });
            Free2DMap.plottingManager.addPlottingObject(fePoint);
            fePoint.infoObj = params.infoObj;
        },
        createMark3D(params) {
            const mark = new FreeXEarth.FeMark({
                position: params.position,
                name: params.name,
                image: params.image,
                imageSize: 32,
                show: true,
            });
            FreeEarth.addFeature(mark);
            mark.id = params.id;
            mark.infoObj = params.infoObj;
            mark.label.entity.label.pixelOffset = new Cesium.Cartesian2(10, 0);
            mark.label.entity.label.font = "16px 宋体";
            mark.label.entity.label.showBackground = true;
            mark.label.entity.label.horizontalOrigin =
                Cesium.HorizontalOrigin.LEFT;
            this.addToArray(params, mark);
        },
        addToArray(params, mark) {
            window[params.type + "Array"] &&
                window[params.type + "Array"].set(params.id, mark);
        },
        removeMark(type, sourceId) {
            let values = [];
            if (window[type + "Array"]) {
                values = [...window[type + "Array"].values];
            }
            if (type === "team") {
                if (sourceId) {
                    const sourceValues = [];
                    forEach(values, (v) => {
                        if (v.infoObj.get("source") == sourceId) {
                            sourceValues.push(v);
                        }
                    });
                    values = [...sourceValues];
                }
                forEach(values, (v) => {
                    window.teamArray.remove(v.id);
                });
            } else {
                window[type + "Array"] && window[type + "Array"].removeAll();
            }
            // if (type === "goals") {
            //     this.goalsArray.removeAll();
            // } else if (type === "equip") {
            //     this.equipArray.removeAll();
            // }
            for (let value of values) {
                let id = value.id;
                FreeEarth.removeFeature(value);
                Free2DMap.plottingManager.removePlottingObjectById(id);
            }
        },
        // mark-------------------------end

        // 初始化远程控制功能
        initRemoteControl() {
            try {
                // 初始化远程控制命令管理器
                this.remoteControlManager = new RemoteControlCommandManager(this.$bus)

                // 设置WebSocket的事件总线
                remoteControlWebSocket.setEventBus(this.$bus)

                console.log('远程控制功能初始化成功')
            } catch (error) {
                console.error('远程控制功能初始化失败:', error)
            }
        },
    },
    created() { },
    beforeDestroy() {
        // 清理远程控制资源
        if (this.remoteControlManager) {
            this.remoteControlManager.destroy()
        }
        if (remoteControlWebSocket) {
            remoteControlWebSocket.disconnect()
        }
    },
    mounted() {
        const _vue = this;
        window.FreeIntegrated = new FreeXIntegrated.FeIntegratedManager();
        window.Free2DMap = FreeIntegrated.createMap("map-2d");
        window.FreeEarth = FreeIntegrated.createEarth("map-3d");
        this.$nextTick(() => {
            FreeEarth.clock.shouldAnimate = true;
        })

        // 创建与物理沙盘联接的WebSocket客户端
        LinkageWebSocketClient.createWebSocketClient()

        // 初始化远程控制功能
        this.initRemoteControl()
        window.linkageByData = (data) => {
            if (data) {
                if (data.code === 2001) {
                    console.log("点位指令交互", data);
                    MAP_MASK.forEach((item) => {
                        if (data.msg == item.instruction) {
                            this.$bus.$emit('chooseMapMaskItem', item)
                            return
                        }
                    })
                    if (timer) {
                        clearInterval(timer)
                        timer = null
                    }
                    if (data.msg == 'FangDa') {    // 放大
                        let zoom = Free2DMap.getOL().getView().getZoom()
                        if (zoom < 22) {
                            Free2DMap.getOL().getView().setZoom(zoom + 1)
                        }
                    } else if (data.msg == 'SuoXiao') {    // 缩小
                        let zoom = Free2DMap.getOL().getView().getZoom()
                        if (zoom > 1) {
                            Free2DMap.getOL().getView().setZoom(zoom - 1)
                        }
                    } else if (data.msg == 'ShangYang') {    // 上仰
                        let pitch = Cesium.Math.toDegrees(FreeEarth.camera.pitch)
                        pitch = pitch + 10;
                        if (pitch > 90) { pitch = 90 }
                        FreeEarth.camera.setView({
                            destination: FreeEarth.camera.position,
                            orientation: {
                                pitch: Cesium.Math.toRadians(pitch),
                                heading: FreeEarth.camera.heading,
                                roll: FreeEarth.camera.roll
                            }
                        })
                    } else if (data.msg == 'XiaFu') {    // 下俯
                        let pitch = Cesium.Math.toDegrees(FreeEarth.camera.pitch)
                        pitch = pitch - 10;
                        if (pitch < -90) { pitch = -90 }
                        FreeEarth.camera.setView({
                            destination: FreeEarth.camera.position,
                            orientation: {
                                pitch: Cesium.Math.toRadians(pitch),
                                heading: FreeEarth.camera.heading,
                                roll: FreeEarth.camera.roll
                            }
                        })
                    } else if (data.msg == 'ZuoYi') {    // 左移
                        timer = setInterval(() => {
                            let angle = FreeEarth.camera.positionCartographic.height / 100000000;
                            FreeEarth.camera.rotate(FreeEarth.camera.upWC, Cesium.Math.toRadians(angle));
                        }, 20)
                    } else if (data.msg == 'YouYi') {    // 右移
                        timer = setInterval(() => {
                            let angle = FreeEarth.camera.positionCartographic.height / 100000000;
                            FreeEarth.camera.rotate(FreeEarth.camera.upWC, Cesium.Math.toRadians(-angle));
                        }, 20)
                    } else if (data.msg == 'QianJin') {    // 前进
                        timer = setInterval(() => {
                            let angle = FreeEarth.camera.positionCartographic.height / 100000000;
                            FreeEarth.camera.rotate(FreeEarth.camera.rightWC, Cesium.Math.toRadians(angle));
                        }, 20)
                    } else if (data.msg == 'HouTui') {    // 后退
                        timer = setInterval(() => {
                            let angle = FreeEarth.camera.positionCartographic.height / 100000000;
                            FreeEarth.camera.rotate(FreeEarth.camera.rightWC, Cesium.Math.toRadians(-angle));
                        }, 20)
                    } else if (data.msg == 'ZuoZhuan') {    // 左转
                        var position = FreeEarth.camera.position;
                        var pitch = FreeEarth.camera.pitch
                        timer = setInterval(() => {
                            var heading = FreeEarth.camera.heading - Cesium.Math.toRadians(0.1);
                            FreeEarth.scene.camera.setView({
                                destination: position,
                                orientation: {
                                    heading: heading,
                                    pitch: pitch,
                                },
                            });
                        }, 30);
                    } else if (data.msg == 'YouZhuan') {    // 右转
                        var position = FreeEarth.camera.position;
                        var pitch = FreeEarth.camera.pitch
                        timer = setInterval(() => {
                            var heading = FreeEarth.camera.heading - Cesium.Math.toRadians(-0.1);
                            FreeEarth.scene.camera.setView({
                                destination: position,
                                orientation: {
                                    heading: heading,
                                    pitch: pitch,
                                },
                            });
                        }, 30);
                    } else if (data.msg == 'ErSanWeiYiTiHua') {    // 二三维一体化
                        this.changeMapMode('23D')
                    } else if (data.msg == 'ErWeiMoShi') {    // 二维
                        this.changeMapMode('2D')
                    } else if (data.msg == 'SanWeiMoShi') {    // 三维
                        this.changeMapMode('3D')
                    } else if (data.msg == 'MoRenShiJiao') {    // 默认视角
                        FreeEarth.navigator.resetView();
                    } else if (data.msg == 'ZhengBei') {    // 正北
                        FreeEarth.navigator.resetCameraRotation();
                    } else if (data.msg == 'MuBiaoQuanXian') {    // 目标全显
                        this.$bus.$emit("showAllTarget")
                    } else if (data.msg == 'MuBiaoQuanYin') {    // 目标全隐
                        this.$bus.$emit("hideAllTarget")
                    } else if (data.msg == 'QuanJingQuanXian') {    // 全景全显
                        this.$bus.$emit("showAllPanorama")
                    } else if (data.msg == 'QuanJingQuanYin') {    // 全景全隐
                        this.$bus.$emit("hideAllPanorama")
                    } else if (data.msg == 'ZiLiaoXianShi') {    // 资料显示
                        this.$bus.$emit("enabledTargetDesctription", true)
                    } else if (data.msg == 'ZiLiaoYinCang') {    // 资料隐藏
                        this.$bus.$emit("enabledTargetDesctription", false)
                    } else if (data.msg == 'TuPian') {    // 图片
                        this.$bus.$emit('changeTargetDescriptionType', '图片')
                    } else if (data.msg == 'WenZi') {    // 文字
                        this.$bus.$emit('changeTargetDescriptionType', '文字')
                    } else if (data.msg == 'ShiPin') {    // 视频
                        this.$bus.$emit('changeTargetDescriptionType', '视频')
                    } else if (data.msg == 'JianKong') {    // 监控
                        this.$bus.$emit('changeTargetDescriptionType', '监控')
                    } else if (data.msg == 'TingZhi') {
                        if (timer) {
                            clearInterval(timer)
                            timer = null
                        }
                    } else {
                        this.$bus.$emit('locationByCode', data.msg)
                    }
                } else if (data.code === 2002) {
                    console.log("不限点位交互", data)
                    let entity = new Cesium.Entity({
                        position: Cesium.Cartesian3.fromDegrees(data.longitude, data.latitude, 0),
                        point: {
                            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                            color: Cesium.Color.TRANSPARENT
                        }
                    })
                    window.FreeEarth.entities.add(entity)
                    FreeEarth.flyTo(entity, {
                        offset: new Cesium.HeadingPitchRange(Cesium.Math.toRadians(Number(data.heading)), Cesium.Math.toRadians(Number(-data.pitch)), Number(data.range)),
                        duration: 3
                    });
                    setTimeout(() => {
                        window.FreeEarth.entities.remove(entity)
                    }, 3100)
                }
            }
        }

        Free2DMap.sceneUtil.setProjection("EPSG:3857");
        this.initMap2D();
        this.initMap3D();
        this.changeViewSync();

        FreeEarth.scene.globe.enableCulling = true;
        FreeEarth.scene.globe.backfaceCulling = true;

        // 三维地球开启标绘拾取
        FreeEarth.enablePick(true);
        // 三维抗锯齿
        // if (Cesium.FeatureDetection.supportsImageRenderingPixelated()) {
        // 	FreeEarth.resolutionScale = window.devicePixelRatio;
        // }

        setTimeout(() => {
            if (!window.graticule) {
                window.graticule = new Graticule({
                    viewer: FreeEarth,
                    lineColor: Cesium.Color.WHITE,
                    sexagesimal: true,
                    numLines: 4,
                });
                FreeEarth.scene.imageryLayers.addImageryProvider(graticule);
                window.graticule.setVisible(true);
            }
        }, 3000)

        // 二维矢量图层 —— 仅添加加载的矢量文件
        let vectorLayerGroup = new ol.layer.Group({ zIndex: 1999 });
        window.Free2DMap.getOL().addLayer(vectorLayerGroup);
        window.vectorLayerGroup = vectorLayerGroup;

        FreeEarth.scene.fxaa = true;
        FreeEarth.scene.postProcessStages.fxaa.enabled = true;
        // 三维军标图层
        let primitives = new Cesium.PrimitiveCollection();
        FreeEarth.scene.primitives.add(primitives);
        FreeEarth.scene.globe.depthTestAgainstTerrain = false;
        window.militaryLayer3D = primitives;
        // 二维军标图层
        let layerGroup = new ol.layer.Group({ zIndex: 1999 });
        window.Free2DMap.getOL().addLayer(layerGroup);
        window.militaryLayer2D = layerGroup;

        window.Free2DMap.getOL().on("moveend", function (e) {
            // TODO: 待优化为缩放时重新计算，即去掉平移地图时调用
            layerGroup.getLayers().forEach((item) => {
                if (item.militaryMark.hasTextArea) {
                    item.militaryMark._createText();
                }
            });
        });

        window.FreeEarth.flyToDest({
            destination: INIT_VIEWPORT,
            rotation: INIT_ROTATION,
            duration: INIT_DURATION,
        });

        // 三维点状标号输入器
        let pointMilitaryInputer3D = new PointMilitaryMarkInputer3D(FreeEarth);
        window.pointMilitaryInputer3D = pointMilitaryInputer3D;
        // 三维线状标号输入器
        let polylineMilitaryInputer3D = new PolylineMilitaryMarkInputer3D(
            FreeEarth
        );
        window.polylineMilitaryInputer3D = polylineMilitaryInputer3D;
        // 三维面状标号输入器
        let polygonMilitaryMarkInputer3D = new PolygonMilitaryMarkInputer3D(
            FreeEarth
        );
        window.polygonMilitaryMarkInputer3D = polygonMilitaryMarkInputer3D;
        // 三维点状标号编辑器
        let pointMilitaryMarkEditer3D = new PointMilitaryMarkEditer3D(
            FreeEarth
        );
        pointMilitaryMarkEditer3D.bind();
        window.pointMilitaryMarkEditer3D = pointMilitaryMarkEditer3D;
        // 三维非点状标号编辑器
        let notPointMilitaryMarkEditer3D = new NotPointMilitaryMarkEditer3D(
            FreeEarth
        );
        notPointMilitaryMarkEditer3D.bind();
        window.notPointMilitaryMarkEditer3D = notPointMilitaryMarkEditer3D;

        // 二维点状标号输入器
        let pointMilitaryInputer2D = new PointMilitaryMarkInputer2D(
            window.Free2DMap.getOL()
        );
        window.pointMilitaryInputer2D = pointMilitaryInputer2D;
        // 二维线状标号输入器
        let polylineMilitaryMarkInputer2D = new PolylineMilitaryMarkInputer2D(
            window.Free2DMap.getOL()
        );
        window.polylineMilitaryMarkInputer2D = polylineMilitaryMarkInputer2D;
        // 二维面状标号输入器
        let polygonMilitaryMarkInputer2D = new PolygonMilitaryMarkInputer2D(
            window.Free2DMap.getOL()
        );
        window.polygonMilitaryMarkInputer2D = polygonMilitaryMarkInputer2D;
        // 二维点状标号编辑器
        let pointMilitaryMarkEditer2D = new PointMilitaryMarkEditer2D(
            window.Free2DMap.getOL()
        );
        window.pointMilitaryMarkEditer2D = pointMilitaryMarkEditer2D;
        // 二维非点状标号编辑器
        let notPointMilitaryMarkEditer2D = new NotPointMilitaryMarkEditer2D(
            window.Free2DMap.getOL()
        );
        notPointMilitaryMarkEditer2D.bind();
        window.notPointMilitaryMarkEditer2D = notPointMilitaryMarkEditer2D;

        // 导航球
        FreeEarth.navigator.setVisible(true);

        // 注册3D标绘拾取事件
        let popups = new Popups3D({ _vue });
        FreeEarth.screenSpaceEventHandler.setInputAction(movement => {
            var pickedFeature = FreeEarth.scene.pick(movement.position);
            // console.log("pickedFeature", pickedFeature)
            if (pickedFeature) {
                var id = undefined, type = undefined
                if (pickedFeature.primitive) {
                    let primitiveType = pickedFeature.primitive.type
                    if (primitiveType) {
                        // 恒歌简单标号拾取
                        if (['closecardinal', 'doublearrow', 'curvearrow', 'beziercurvearrow', 'sectorarea', 'parallelarea', 'polylinearrow'].includes(primitiveType)) {
                            id = pickedFeature.primitive.id
                            type = 'mark'
                        }
                    }
                    if (pickedFeature.id && pickedFeature.id.feature) {
                        // 恒歌简单标绘拾取
                        if (['mark', 'line', 'polygon', 'ellipse', 'rectangle'].includes(pickedFeature.id.feature.type)) {
                            if (!FreeEarth.getMeasureTool().actived && !pickedFeature.id.feature.infoObj) {
                                id = pickedFeature.id.id
                                type = 'mark'
                            }
                        } else if (['dedicated_mark_svg', 'dedicated_mark'].includes(pickedFeature.id.feature.type)) {
                            id = pickedFeature.id.feature.id
                            type = pickedFeature.id.feature.type
                        }
                    }
                    if (pickedFeature.primitive instanceof Cesium.Model) {
                        id = pickedFeature.primitive.id
                        type = 'model'
                    }
                    if (pickedFeature.primitive.marker || pickedFeature.primitive.parent) {
                        if (pickedFeature.primitive.marker) {
                            id = pickedFeature.primitive.marker.id
                        } else if (pickedFeature.primitive.parent) {
                            id = pickedFeature.primitive.parent.id
                        }
                        type = 'military_mark'
                    }
                }

                if (id) {
                    this.$bus.$emit('openEditFn', id, type, false)
                }
                var feature = null;
                if (pickedFeature.id instanceof Cesium.Entity) {
                    if (pickedFeature.id.feature) {
                        feature = pickedFeature.id.feature;
                    }
                } else if (pickedFeature.primitive) {
                    feature = pickedFeature.primitive;
                }
                if (feature && feature.infoObj) {
                    popups.position = feature.position;
                    popups.setContentInfo(feature.infoObj);
                    popups.show = true;
                } else {
                    popups.show = false;
                }
            } else {
                popups.show = false;
            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

        // 注册2D标绘拾取事件
        let popups2d = new Popups2D();
        Free2DMap.getOL().on("singleclick", function (e) {
            let id = undefined, type = undefined
            Free2DMap.getOL().forEachFeatureAtPixel(e.pixel, (feature, layer) => {
                if (layer.militaryType) {
                    id = layer.id
                    type = "military_mark"
                }
            })
            if (id && (window.pointMilitaryMarkEditer2D._isBind || window.notPointMilitaryMarkEditer2D._isBind)) {
                _vue.$bus.$emit('openEditFn2D', id, type, false)
                return
            }
            var features = Free2DMap.getOL().getFeaturesAtPixel(e.pixel, {
                hitTolerance: 1,
            });

            if (features.length > 0) {
                if (features[0].FeFeature && features[0].FeFeature.infoObj) {
                    let feature = features[0].FeFeature;
                    popups2d.position = feature._positions;
                    popups2d.setContentInfo(feature.infoObj);
                    popups2d.show = true;
                } else {
                    popups2d.show = false;
                }
            } else {
                // 点击弹出层上的元素 不关闭
                const eTargetCls = e.originalEvent.target.className;
                if (eTargetCls.indexOf("popups-footer__btn") > -1) {
                    const type = popups2d.mapInfo.get("type");
                    if (type === "goals") {
                        near("team", {
                            distance: popups2d.distance,
                            lat: popups2d.mapInfo.get("纬度"),
                            lng: popups2d.mapInfo.get("经度"),
                        }).then((nearRes) => {
                            _vue.removeMark("team", popups2d.mapInfo.get("id"));
                            _vue.handleLocation(
                                map(nearRes, (nt) => ({
                                    ...nt,
                                    source: popups2d.mapInfo.get("id"),
                                })),
                                "team",
                                false
                            );
                            // popups2d.show = false
                        });
                    } else if (type === "team") {
                        const teamId = popups2d.mapInfo.get("id");
                        _vue.handlerNavBtnClick(
                            { value: "militia", label: "民兵名册" },
                            teamId
                        );
                        // popups2d.show = false
                    }
                } else if (
                    eTargetCls.indexOf("popups-footer__clear-btn") > -1
                ) {
                    const type = popups2d.mapInfo.get("type");
                    if (type === "goals") {
                        _vue.removeMark("team", popups2d.mapInfo.get("id"));
                    }
                } else if (eTargetCls.indexOf("popup__input") > -1) {
                } else popups2d.show = false;
            }
        });

        // 添加扩展操作器
        // const manipulatorFreedomEx = new ManipulatorFreedomEx(FreeEarth);

        // // 清除二维底图并添加新的地图
        // const baseLayers = window.Free2DMap.layerManager.getBaseLayers()
        // baseLayers.forEach(layer => {
        // 	window.Free2DMap.layerManager.removeLayer(layer)
        // })
        // let layer = new ol.layer.Tile({
        // 	source: new ol.source.XYZ({
        // 		crossOrigin: "anonymous",
        // 		url: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
        // 		wrapX: true
        // 	})
        // });
        // window.Free2DMap._ol.addLayer(layer)

        // let layer = new ol.layer.Tile({
        // 	source: new ol.source.XYZ({
        // 		crossOrigin: "anonymous",
        // 		tileUrlFunction: function (tileCoord) {
        // 			let z = tileCoord[0];
        // 			let x = tileCoord[1];
        // 			let y = tileCoord[2];

        // 			let url = 'http://localhost/WeServer/wmts/1.0.0/layer/default/mct/' + z + '/' + y + '/' + x + '.png';
        // 			return url;
        // 		},
        // 		projection: gcj02Mecator,
        // 		wrapX: true,
        // 	})
        // });
        // window.Free2DMap._ol.addLayer(layer)


        document.addEventListener("keydown", (e) => {
            if (e.keyCode === 69) {

            } else if (e.keyCode === 84) {
                //	layer.setVisible(!layer.getVisible())
            } else if (e.keyCode === 49) {

            } else if (e.keyCode === 50) {
            } else if (e.keyCode === 51) {
            } else if (e.keyCode === 52) {

            } else if (e.keyCode === 53) {

            } else if (e.keyCode === 84) {
            }
        }, false);
        // const tileset = FreeEarth.scene.primitives.add(
        //     new Cesium.Cesium3DTileset({
        //         url:
        //             "http://localhost:9090/map-data-services/tilesets/SheQu/tileset.json",
        //             skipLevelOfDetail: true,
        //         baseScreenSpaceError: 2048,
        //         skipScreenSpaceErrorFactor: 16,
        //         skipLevels: 1,
        //         immediatelyLoadDesiredLevelOfDetail: false,
        //         loadSiblings: false,
        //         cullWithChildrenBounds: true
        //     })
        // );

        // tileset.readyPromise.then(function(tileset) {
        //     var boundingSphere = tileset.boundingSphere;
        //     FreeEarth.camera.flyToBoundingSphere(boundingSphere)
        // });

        // function coordinatesArrayToCartesianArray(coordinates, height = 0) {
        // 	const positions = new Array(coordinates.length);
        // 	for (let i = 0; i < coordinates.length; i++) {
        // 		positions[i] = Cesium.Cartesian3.fromDegrees(
        // 			coordinates[i][0],
        // 			coordinates[i][1],
        // 			height
        // 		);
        // 	}
        // 	return positions;
        // }

        // const positions = [];
        // positions.push([113.59326, 34.79738]);
        // positions.push([113.5966, 34.79739]);
        // positions.push([113.59698, 34.7996]);
        // positions.push([113.59268, 34.79958]);
        // FreeEarth.entities.add({
        // 	polygon: {
        // 		hierarchy: new Cesium.PolygonHierarchy(
        // 			coordinatesArrayToCartesianArray(positions)
        // 		),
        // 		// material: new Cesium.Color(71 / 255.0, 5 / 255.0, 255 / 255.0, 0.5),
        // 		material: Cesium.Color.DODGERBLUE.withAlpha(0.6),
        // 		//     material: new Cesium.Color(4 / 255.0, 147 / 255.0, 156 / 255.0  , 0.5),
        // 		closeBottom: false,
        // 		closeTop: false,
        // 		height: 0,
        // 		extrudedHeight: 120,
        // 		// material: new Cesium.Color(19 / 255.0, 25 / 255.0, 41 / 255.0, 0.5),
        // 	},
        // });

        // 响应事件：创建二、三维标绘
        // 调用方法：this.$bus.$emit('createMark', params)
        // 参数说明：params = {
        //                    position: [lon, lat, alt],
        //                    name: "名称",
        //                    image: "SVG_URL",
        //                    infoObj: infoObj
        //                  }
        this.$bus.$on("createMark", (params) => {
            if (!isArray(params)) {
                this.createMark(params);
            } else {
                if (params.length === 1) {
                    this.createMark(params[0]);
                    const options = {
                        destination: [
                            params[0].position[0],
                            params[0].position[1],
                            2000.0,
                        ],
                        rotation: [0.0, -90.0, 0.0],
                        duration: 3.0,
                    };
                    FreeEarth.flyToDest(options);
                } else {
                    forEach(params, (value) => {
                        this.createMark(value);
                    });
                }
            }
        });

        // 响应事件： 移除二三维标绘
        this.$bus.$on("removeMark", (type) => {
            if (type === "all") {
                const marks = [
                    "goals",
                    "equip",
                    "team",
                ];
                forEach(marks, (m) => _vue.removeMark(m));
            } else this.removeMark(type);
        });

    },
};
</script>

<style lang="scss">
.home {
    width: 100%;
    height: 100%;

    .container {
        width: 100%;
        height: calc(100% - 0.55rem);
        display: flex;

        .ThreeDHome,
        .TwoDHome,
        .earth-contanier {
            width: 100%;
            height: 100%;
        }

        .half {
            width: 50%;
        }
    }

}

.invisible {
    position: fixed !important;
    width: 100% !important;
    visibility: hidden;
    z-index: -100;
}
</style>
