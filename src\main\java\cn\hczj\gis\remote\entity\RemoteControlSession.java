package cn.hczj.gis.remote.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 远程控制会话实体
 */
@Data
public class RemoteControlSession {
    
    /**
     * 控制码
     */
    private String controlCode;
    
    /**
     * 被控制端用户ID
     */
    private String controlledUserId;
    
    /**
     * 控制端用户ID
     */
    private String controllerUserId;
    
    /**
     * 会话状态：WAITING(等待连接), CONNECTED(已连接), DISCONNECTED(已断开)
     */
    private String status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 过期时间
     */
    private LocalDateTime expireTime;
    
    /**
     * 连接时间
     */
    private LocalDateTime connectTime;
    
    /**
     * 断开时间
     */
    private LocalDateTime disconnectTime;
    
    public RemoteControlSession() {
        this.createTime = LocalDateTime.now();
        this.expireTime = LocalDateTime.now().plusMinutes(10); // 10分钟过期
        this.status = "WAITING";
    }
    
    public RemoteControlSession(String controlCode, String controlledUserId) {
        this();
        this.controlCode = controlCode;
        this.controlledUserId = controlledUserId;
    }
    
    /**
     * 检查会话是否过期
     */
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expireTime);
    }
    
    /**
     * 检查会话是否有效（未过期且状态正确）
     */
    public boolean isValid() {
        return !isExpired() && ("WAITING".equals(status) || "CONNECTED".equals(status));
    }
}
