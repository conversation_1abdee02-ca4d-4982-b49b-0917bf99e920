/**
 * 远程控制WebSocket客户端
 */
class RemoteControlWebSocket {
    constructor() {
        this.websocket = null
        this.userId = null
        this.isConnected = false
        this.reconnectAttempts = 0
        this.maxReconnectAttempts = 5
        this.reconnectInterval = 3000
        this.messageHandlers = new Map()
        this.eventBus = null
    }

    /**
     * 设置事件总线
     */
    setEventBus(eventBus) {
        this.eventBus = eventBus
    }

    /**
     * 连接WebSocket
     */
    connect(userId) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            console.log('WebSocket已连接')
            return Promise.resolve()
        }

        this.userId = userId
        // 由于这是纯前端项目，我们使用模拟的WebSocket URL
        const wsUrl = `ws://localhost:8092/gis-services/remote-control/${userId}`

        return new Promise((resolve, reject) => {
            try {
                this.websocket = new WebSocket(wsUrl)

                this.websocket.onopen = () => {
                    console.log('远程控制WebSocket连接成功')
                    this.isConnected = true
                    this.reconnectAttempts = 0
                    resolve()
                }

                this.websocket.onmessage = (event) => {
                    this.handleMessage(event.data)
                }

                this.websocket.onerror = (error) => {
                    console.error('远程控制WebSocket错误:', error)
                    this.isConnected = false
                    reject(error)
                }

                this.websocket.onclose = (event) => {
                    console.log('远程控制WebSocket连接关闭:', event.code, event.reason)
                    this.isConnected = false

                    // 如果不是主动关闭，尝试重连
                    if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
                        this.attemptReconnect()
                    }
                }
            } catch (error) {
                console.error('创建WebSocket连接失败:', error)
                reject(error)
            }
        })
    }

    /**
     * 断开连接
     */
    disconnect() {
        if (this.websocket) {
            this.websocket.close(1000, '主动断开')
            this.websocket = null
        }
        this.isConnected = false
        this.userId = null
    }

    /**
     * 发送消息
     */
    send(message) {
        if (!this.isConnected || !this.websocket) {
            console.warn('WebSocket未连接，无法发送消息')
            return false
        }

        try {
            const messageStr = typeof message === 'string' ? message : JSON.stringify(message)
            this.websocket.send(messageStr)
            return true
        } catch (error) {
            console.error('发送WebSocket消息失败:', error)
            return false
        }
    }

    /**
     * 请求控制码
     */
    requestControlCode() {
        return this.send({
            type: 'REQUEST_CONTROL_CODE'
        })
    }

    /**
     * 通过控制码连接
     */
    connectWithCode(controlCode) {
        return this.send({
            type: 'CONNECT_WITH_CODE',
            controlCode: controlCode
        })
    }

    /**
     * 断开控制连接
     */
    disconnectControl() {
        return this.send({
            type: 'DISCONNECT'
        })
    }

    /**
     * 发送控制命令
     */
    sendControlCommand(command, data = {}) {
        return this.send({
            type: 'CONTROL_COMMAND',
            data: {
                command: command,
                ...data,
                timestamp: Date.now()
            }
        })
    }

    /**
     * 注册消息处理器
     */
    onMessage(type, handler) {
        if (!this.messageHandlers.has(type)) {
            this.messageHandlers.set(type, [])
        }
        this.messageHandlers.get(type).push(handler)
    }

    /**
     * 移除消息处理器
     */
    offMessage(type, handler) {
        if (this.messageHandlers.has(type)) {
            const handlers = this.messageHandlers.get(type)
            const index = handlers.indexOf(handler)
            if (index > -1) {
                handlers.splice(index, 1)
            }
        }
    }

    /**
     * 处理接收到的消息
     */
    handleMessage(data) {
        try {
            const message = JSON.parse(data)
            console.log('收到远程控制消息:', message)

            // 调用注册的处理器
            if (this.messageHandlers.has(message.type)) {
                const handlers = this.messageHandlers.get(message.type)
                handlers.forEach(handler => {
                    try {
                        handler(message)
                    } catch (error) {
                        console.error('消息处理器执行失败:', error)
                    }
                })
            }

            // 处理控制命令
            if (message.type === 'CONTROL_COMMAND') {
                this.handleControlCommand(message)
            }

        } catch (error) {
            console.error('解析WebSocket消息失败:', error)
        }
    }

    /**
     * 处理控制命令
     */
    handleControlCommand(message) {
        if (!message.data || !message.data.command) {
            console.warn('无效的控制命令:', message)
            return
        }

        const { command } = message.data
        console.log('执行控制命令:', command)

        // 通过EventBus发送控制命令
        if (this.eventBus) {
            this.eventBus.$emit('remote-control-command', {
                command: command,
                data: message.data
            })
        }

        // 直接执行一些基本命令
        this.executeCommand(command, message.data)
    }

    /**
     * 执行控制命令
     */
    executeCommand(command, data) {
        try {
            switch (command) {
                case 'ZOOM_IN':
                    if (window.FreeEarth && window.FreeEarth.navigator) {
                        window.FreeEarth.navigator.zoomInStart()
                        setTimeout(() => {
                            window.FreeEarth.navigator.zoomInEnd()
                        }, 500)
                    }
                    break

                case 'ZOOM_OUT':
                    if (window.FreeEarth && window.FreeEarth.navigator) {
                        window.FreeEarth.navigator.zoomOutStart()
                        setTimeout(() => {
                            window.FreeEarth.navigator.zoomOutEnd()
                        }, 500)
                    }
                    break

                case 'RESET_VIEW':
                    if (window.FreeEarth && window.FreeEarth.navigator) {
                        window.FreeEarth.navigator.resetView()
                    }
                    break

                case 'RESET_NORTH':
                    if (window.FreeEarth && window.FreeEarth.navigator) {
                        window.FreeEarth.navigator.resetCameraRotation()
                    }
                    break

                default:
                    console.warn('未知的控制命令:', command)
            }
        } catch (error) {
            console.error('执行控制命令失败:', error)
        }
    }

    /**
     * 尝试重连
     */
    attemptReconnect() {
        this.reconnectAttempts++
        console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)

        setTimeout(() => {
            if (this.userId) {
                this.connect(this.userId).catch(error => {
                    console.error('重连失败:', error)
                })
            }
        }, this.reconnectInterval)
    }

    /**
     * 获取连接状态
     */
    getConnectionState() {
        return {
            isConnected: this.isConnected,
            userId: this.userId,
            readyState: this.websocket ? this.websocket.readyState : WebSocket.CLOSED
        }
    }
}

// 创建单例实例
const remoteControlWebSocket = new RemoteControlWebSocket()

export default remoteControlWebSocket
