{"type": "MapBoxStyle", "style": [{"key": "landuse_park_polygon", "layer": "landuse", "cls": "park", "type": "polygon", "fillColor": "rgb(47,53,47)"}, {"key": "landuse_cemetery_polygon", "layer": "landuse", "cls": "cemetery", "type": "polygon", "fillColor": "rgb(48,48,47)"}, {"key": "landuse_hospital_polygon", "layer": "landuse", "cls": "hospital", "type": "polygon", "fillColor": "rgb(53,47,47)"}, {"key": "landuse_school_polygon", "layer": "landuse", "cls": "school", "type": "polygon", "fillColor": "rgb(48,48,47)"}, {"key": "landuse_wood_polygon", "layer": "landuse", "cls": "wood", "type": "polygon", "fillColor": "rgb(55,54,53)"}, {"key": "waterway_default_line", "layer": "waterway", "cls": "", "type": "line", "width": 1, "strokeColor": "rgb(34,34,34)"}, {"key": "waterway_river_line", "layer": "waterway", "cls": "river", "type": "line", "width": 1, "strokeColor": "rgb(34,34,34)"}, {"key": "waterway_stream_line", "layer": "waterway", "cls": "stream", "type": "line", "width": 1, "strokeColor": "rgb(34,34,34)"}, {"key": "water_default_polygon", "layer": "water", "cls": "", "type": "polygon", "fillColor": "rgb(34,34,34)"}, {"key": "aeroway_Polygon_polygon", "layer": "aeroway", "cls": "", "geom": "Polygon", "type": "polygon", "fillColor": "rgb(242,239,235)"}, {"key": "aeroway_LineString_line", "layer": "aeroway", "geom": "LineString", "cls": "", "type": "line", "width": 1, "strokeColor": "#f0ede9"}, {"key": "building_default_strokedPolygon", "layer": "building", "cls": "", "type": "strokedPolygon", "width": 1, "fillColor": "rgb(48,48,47)", "strokeColor": "rgb(48,48,47)"}, {"key": "tunnel_motorway_link_line", "layer": "tunnel", "cls": "motorway_link", "type": "line", "width": 1, "strokeColor": "rgb(70,70,70)"}, {"key": "tunnel_service_line", "layer": "tunnel", "cls": "service", "type": "line", "width": 1, "strokeColor": "rgb(70,70,70)"}, {"key": "tunnel_street_line", "layer": "tunnel", "cls": "street", "type": "line", "width": 1, "strokeColor": "rgb(70,70,70)"}, {"key": "tunnel_street_limited_line", "layer": "tunnel", "cls": "street_limited", "type": "line", "width": 1, "strokeColor": "rgb(70,70,70)"}, {"key": "tunnel_main_line", "layer": "tunnel", "cls": "main", "type": "line", "width": 1, "strokeColor": "rgb(70,70,70)"}, {"key": "tunnel_default_resolutionMax_line", "layer": "tunnel", "cls": "", "resolutionMax": 1222.99245256282, "type": "line", "width": 1, "strokeColor": "rgb(70,70,70)"}, {"key": "tunnel_default_line", "layer": "tunnel", "cls": "", "type": "line", "width": 1, "strokeColor": "rgb(70,70,70)"}, {"key": "tunnel_motorway_line", "layer": "tunnel", "cls": "motorway", "type": "line", "width": 1, "strokeColor": "rgb(70,70,70)"}, {"key": "tunnel_path_line", "layer": "tunnel", "cls": "path", "type": "line", "width": 1, "strokeColor": "#cba"}, {"key": "tunnel_major_rail_line", "layer": "tunnel", "cls": "major_rail", "type": "line", "width": 2, "strokeColor": "rgb(48,48,47)"}, {"key": "road_motorway_link_line", "layer": "road", "cls": "motorway_link", "type": "line", "width": 2, "strokeColor": "rgb(70,70,70)"}, {"key": "road_street_LineString_line", "layer": "road", "cls": "street", "geom": "LineString", "type": "line", "width": 1, "strokeColor": "rgb(70,70,70)"}, {"key": "road_street_limited_LineString_line", "layer": "road", "cls": "street_limited", "geom": "LineString", "type": "line", "width": 1, "strokeColor": "rgb(70,70,70)"}, {"key": "road_main_resolutionMax_line", "layer": "road", "cls": "main", "resolutionMax": 1222.99245256282, "type": "line", "width": 1, "strokeColor": "rgb(70,70,70)"}, {"key": "road_motorway_resolutionMax_line", "layer": "road", "cls": "motorway", "resolutionMax": 4891.96981025128, "type": "line", "width": 1, "strokeColor": "rgb(70,70,70)"}, {"key": "road_path_line", "layer": "road", "cls": "path", "type": "line", "width": 1, "strokeColor": "rgb(70,70,70)"}, {"key": "road_major_rail_line", "layer": "road", "cls": "major_rail", "type": "line", "width": 2, "strokeColor": "rgb(70,70,70)"}, {"key": "bridge_motorway_link_line", "layer": "bridge", "cls": "motorway_link", "type": "line", "width": 1, "strokeColor": "rgb(70,70,70)"}, {"key": "bridge_motorway_line", "layer": "bridge", "cls": "motorway", "type": "line", "width": 1, "strokeColor": "rgb(70,70,70)"}, {"key": "bridge_service_line", "layer": "bridge", "cls": "service", "type": "line", "width": 1, "strokeColor": "rgb(70,70,70)"}, {"key": "bridge_street_line", "layer": "bridge", "cls": "street", "type": "line", "width": 1, "strokeColor": "rgb(70,70,70)"}, {"key": "bridge_street_limited_line", "layer": "bridge", "cls": "street_limited", "type": "line", "width": 1, "strokeColor": "rgb(70,70,70)"}, {"key": "bridge_main_resolutionMax_line", "layer": "bridge", "cls": "main", "resolutionMax": 1222.99245256282, "type": "line", "width": 1, "strokeColor": "rgb(70,70,70)"}, {"key": "bridge_path_line", "layer": "bridge", "cls": "path", "type": "line", "width": 1, "strokeColor": "#cba"}, {"key": "bridge_major_rail_line", "layer": "bridge", "cls": "major_rail", "type": "line", "width": 2, "strokeColor": "rgb(70,70,70)"}, {"key": "admin_adminLevel3_maritime0_line", "layer": "admin", "adminLevel": 3, "maritime": 0, "type": "line", "width": 1, "strokeColor": "rgb(70,70,70)"}, {"key": "admin_adminLevel2_disputed0_maritime0_line", "layer": "admin", "adminLevel": 2, "disputed": 0, "maritime": 0, "type": "line", "width": 1, "strokeColor": "rgb(90,90,90)"}, {"key": "admin_adminLevel2_disputed1_maritime0_line", "layer": "admin", "adminLevel": 2, "disputed": 1, "maritime": 0, "type": "line", "width": 1, "strokeColor": "rgb(90,90,90)"}, {"key": "admin_adminLevel3_maritime1_line", "layer": "admin", "adminLevel": 3, "maritime": 1, "type": "line", "width": 1, "strokeColor": "rgb(90,90,90)"}, {"key": "admin_adminLevel2_maritime1_line", "layer": "admin", "adminLevel": 2, "maritime": 1, "type": "line", "width": 1, "strokeColor": "rgb(90,90,90)"}, {"key": "country_label_scalerank", "font": "18px 微软雅黑", "fillColor": [150, 150, 150], "strokeColor": [0, 0, 0, 0.8], "width": 1}, {"key": "marine_label_labelrank", "font": "18px 微软雅黑", "fillColor": [150, 150, 150], "strokeColor": [0, 0, 0, 0.8], "width": 1}, {"key": "place_label_city", "font": "18px 微软雅黑", "fillColor": [150, 150, 150], "strokeColor": [0, 0, 0, 0.8], "width": 1}, {"key": "place_label_town", "font": "18px 微软雅黑", "fillColor": [150, 150, 150], "strokeColor": [0, 0, 0, 0.8], "width": 1}, {"key": "place_label_village", "font": "18px 微软雅黑", "fillColor": [150, 150, 150], "strokeColor": [0, 0, 0, 0.8], "width": 1}, {"key": "place_label_hamlet", "font": "18px 微软雅黑", "fillColor": [150, 150, 150], "strokeColor": [0, 0, 0, 0.8], "width": 1}, {"key": "Point", "style": 0, "font": "14px 微软雅黑", "fillColor": [255, 255, 255]}]}