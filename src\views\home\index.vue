<template>
    <div class="map-box">
        <div class="header">
            <div class="header-left">
                <img class="header-logo" alt="" src="../../assets/img/head/new.png"/>
                <i :style="{fontSize: platformTitleFontSize}">{{ platformTitle}}</i>
            </div>
            <div class="header-center">
                  <div :class="{'header-right-btn': true, 'header-right-btn-sel': mapMode === '23D'}"
                     @click.self="changeMapMode('23D')">一体化
                </div>
                <div :class="{'header-right-btn': true, 'header-right-btn-sel': mapMode === '3D'}"
                     @click.self="changeMapMode('3D')">三维视图
                </div>
                <div :class="{'header-right-btn': true, 'header-right-btn-sel': mapMode === '2D'}"
                     @click.self="changeMapMode('2D')">二维视图
                </div>
                <div v-if="mapMode === '23D'"
                     :class="{'header-right-btn': true, 'header-right-btn-sel': isViewSyncOpen}"
                     @click.self="changeViewSync">视图同步
                </div>
            </div>

            <div class="header-right">
                <!-- 远程控制按钮区域 -->
                <div class="remote-control-buttons">
                    <el-button
                        type="primary"
                        size="small"
                        icon="el-icon-phone"
                        @click="requestAssistance"
                        class="remote-btn request-btn">
                        请求协助
                    </el-button>
                    <el-button
                        type="success"
                        size="small"
                        icon="el-icon-monitor"
                        @click="startRemoteControl"
                        class="remote-btn control-btn">
                        远程控制
                    </el-button>
                </div>

                <div class="header-time">
                    <div class="header-astronomy-time">
                        <span>天文时间</span>
                        <i>{{ astronomyTimeStr }}</i>
                    </div>
                    <div class="header-fight-time" @dblclick="showFightForm" @click="handleControl">
                        <span>作战时间</span>
                        <i>{{ fightTimeStr }}</i>
                    </div>
                </div>

                <!-- 用户信息区域，在非免登录模式下显示 -->
                <div class="user-info" v-if="user && !noLoginMode">
                    <el-dropdown  @command="handleCommand">
                        <span class="el-dropdown-link">
                            <img v-if="user.avatar" :src="baseUrl + user.avatar" class="avatar-img" />
                            <i v-else class="el-icon-user-solid avatar-icon"></i>
                            {{ user.nickname || user.username }}<i class="el-icon-arrow-down el-icon--right"></i>
                        </span>
                        <el-dropdown-menu class="custom-dropDown" slot="dropdown" >
                            <el-dropdown-item command="profileEdit">修改资料</el-dropdown-item>
                            <el-dropdown-item command="changePassword">修改密码</el-dropdown-item>
                            <el-dropdown-item v-if="isAdmin" command="userManage">用户管理</el-dropdown-item>
                            <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </div>

                <JfqPopover
                    popper-class="header-catalogue-popover"
                    :visible-arrow="false"
                    placement="left"
                    img="head/catalogue.png"
                >
                    <el-menu
                        default-active="1"
                        class="header-catalogue-popover-menu"
                        :unique-opened="true"
                        @select="onMenuSelect"
                    >
                        <el-submenu index="1" style="margin-top: 80px;">
                            <template slot="title">
                                <span>随手标绘</span>
                            </template>
                            <el-menu-item index="1-3" @click="startFreeLine()">开启随手标绘</el-menu-item>
                            <el-menu-item index="1-3-1" @click="clearFreeLine()">清除随手标绘</el-menu-item>
                            <el-menu-item index="1-3-2" @click="closeFreeLine()">关闭随手标绘</el-menu-item>
                        </el-submenu>
                        <el-submenu index="4">
                            <template slot="title">
                                <span>防控范围</span>
                            </template>
                            <el-menu-item index="4-1" @click="preventionArea('五分钟防控范围')">五分钟范围</el-menu-item>
                            <el-menu-item index="4-2" @click="preventionArea('十分钟防控范围')">十分钟范围</el-menu-item>
                            <el-menu-item index="4-3" @click="preventionArea('三十分钟防控范围')">三十分钟范围</el-menu-item>
                            <el-menu-item index="4-4" @click="clearPreventionArea()">清空范围</el-menu-item>
                        </el-submenu>
                    </el-menu>
                </JfqPopover>
            </div>
        </div>
        <MapContainer ref="MapContainer" @onChangeViewSync="onChangeViewSync"/>
        <JfqFooter/>

        <!-- IM聊天组件，在非免登录模式下显示 -->
        <IM v-if="!noLoginMode" />

        <!-- 地址检索组件 -->
        <CitySearch />

        <JfqDialog
            ref="updatefighttime-add-dialog"
            :title="'修改作战时间'"
            :modal-append-to-body="false"
            :close-on-click-modal="false"
            top="10%"
            width="45%"
            navscreen
            form-mode
            submit-text="保存"
            custom-class="fightform"
            @refresh="updateFightTime"
        >
            <template #slot-content>
                <FightTimeForm ref="dialog-add-form"/>
            </template>
        </JfqDialog>

        <!-- 用户管理对话框 -->
        <UserManage ref="userManageDialog" v-if="showUserManageDialog" @close="closeUserManage" />

        <!-- 修改密码对话框 -->
        <div v-if="showChangePasswordDialog" class="dialog-overlay">
          <ChangePassword ref="changePasswordDialog" @close="closeChangePassword" />
        </div>

        <!-- 修改资料对话框 -->
        <div v-if="showProfileEditDialog" class="dialog-overlay">
          <ProfileEdit ref="profileEditDialog" @close="closeProfileEdit" />
        </div>

        <!-- 远程控制对话框 -->
        <RequestAssistance
            ref="requestAssistanceDialog"
            v-if="showRequestAssistanceDialog"
            @close="closeRequestAssistance" />

        <RemoteControl
            ref="remoteControlDialog"
            v-if="showRemoteControlDialog"
            @close="closeRemoteControl" />
    </div>
</template>
<script>
import dayjs from "dayjs";
import MapContainer from '../map/MapContainer'
import JfqPopover from '@/components/Popover/index'
import JfqFooter from './Footer'
import FightTimeForm from './FightTimeForm'
import JfqDialog from "@/components/Dialog"
import IM from '@/components/IM'
import UserManage from '@/components/UserManage'
import ChangePassword from '@/components/ChangePassword'
import ProfileEdit from '@/components/ProfileEdit'
import CitySearch from '@/components/CitySearch'
import RequestAssistance from '@/components/RemoteControl/RequestAssistance'
import RemoteControl from '@/components/RemoteControl/RemoteControl'

import FreeLine from './tempJS/FreeLine.js'
import PreventionArea from './tempJS/PreventionArea.js'
import moment from "moment"
import { mapGetters, mapMutations, mapActions } from 'vuex'

var freeLine = null
var preventionArea = null

export default {
    name: 'HomeIndex',
    components: {
        MapContainer,
        JfqPopover,
        JfqFooter,
        FightTimeForm,
        JfqDialog,
        IM,
        UserManage,
        ChangePassword,
        ProfileEdit,
        CitySearch,
        RequestAssistance,
        RemoteControl
    },
    data() {
        return {
            astronomyTimeStr: dayjs().format('YYYY/MM/DD HH:mm:ss'),
            fightTimeStr: dayjs().format('YYYY/MM/DD HH:mm:ss'),
            isViewSyncOpen: false,
            fightTimeDiff: 0,
            showUserManageDialog: false,
            showChangePasswordDialog: false,
            showProfileEditDialog: false,
            baseUrl: GIS_SERVICES,

            platformTitle: PLATFORM_TITLE,
            platformTitleFontSize: PLATFORM_TITLE_FONT_SIZE,

            // 远程控制相关
            showRequestAssistanceDialog: false,
            showRemoteControlDialog: false,
        }
    },
    computed: {
        ...mapGetters("map", ["mapMode"]),
        ...mapGetters("auth", ["user", "isAdmin"]),
        // 是否为免登录模式
        noLoginMode() {
            return NO_LOGIN_MODE === true
        }
    },
    methods: {
        ...mapMutations("map", ["changeMapMode"]),
        ...mapMutations("sceneModule", ["changeNavDialogMode"]),
        ...mapMutations("layerLoad", ['changeFzkzVisible']),
        ...mapActions("auth", ["logout"]),

        // 处理下拉菜单命令
        handleCommand(command) {
            if (command === 'logout') {
                this.handleLogout()
            } else if (command === 'userManage') {
                this.showUserManage()
            } else if (command === 'changePassword') {
                this.showChangePassword()
            } else if (command === 'profileEdit') {
                this.showProfileEdit()
            }
        },

        // 显示修改密码对话框
        showChangePassword() {
            this.showChangePasswordDialog = true
            this.$nextTick(() => {
                this.$refs.changePasswordDialog.show()
            })
        },

        // 关闭修改密码对话框
        closeChangePassword() {
            this.showChangePasswordDialog = false
        },

        // 显示修改资料对话框
        showProfileEdit() {
            this.showProfileEditDialog = true
            this.$nextTick(() => {
                this.$refs.profileEditDialog.show()
            })
        },

        // 关闭修改资料对话框
        closeProfileEdit() {
            this.showProfileEditDialog = false
        },

        // 显示用户管理对话框
        showUserManage() {
            this.showUserManageDialog = true
            this.$nextTick(() => {
                this.$refs.userManageDialog.show()
            })
        },

        // 关闭用户管理对话框
        closeUserManage() {
            this.showUserManageDialog = false
        },

        // 处理登出
        handleLogout() {
            this.logout().then(() => {
                this.$message.success('退出登录成功')
                this.$router.push('/login')
            })
        },
        handleControl(){
           this.changeFzkzVisible(true)
        },
        startFreeLine() {
            if (!freeLine) {
                freeLine = new FreeLine(FreeEarth)
            }
        },
        clearFreeLine () {
            if (freeLine) {
                freeLine.clear()
            }
        },
        closeFreeLine () {
            if (freeLine) {
                freeLine.destroy()
                freeLine = null
            }
        },
        onMenuSelect(menu) {
            if (menu === '1-2') {
                // this.$refs['MapContainer'].showNavBtn()
                this.changeNavDialogMode('military_mark');
            }
        },
        showFightForm() {
            this.$refs["updatefighttime-add-dialog"].show({fightTime: dayjs(this.fightTimeStr)})
        },
        updateFightTime(fightTime) {
            this.fightTimeDiff = dayjs(fightTime).diff(new Date(), 'millisecond')
        },
        changeViewSync() {
            this.$refs.MapContainer.changeViewSync()
        },
        onChangeViewSync(isViewSyncOpen) {
            this.isViewSyncOpen = isViewSyncOpen
        },
        getTimeStr() {
            this.astronomyTimeStr = dayjs().format('YYYY/MM/DD HH:mm:ss')
            // this.fightTimeStr = dayjs().add(this.fightTimeDiff, 'millisecond').format('YYYY/MM/DD HH:mm:ss')
            this.fightTimeStr = moment(Cesium.JulianDate.toDate(window.FreeEarth.clock.currentTime)).format("YYYY/MM/DD HH:mm:ss");
            this.timer = setTimeout(this.getTimeStr, 100)
        },
        preventionArea(name) {
            if (!preventionArea) {
                preventionArea = new PreventionArea(FreeEarth)
            }
            preventionArea.name = name
            preventionArea.bind()
        },
        clearPreventionArea () {
            if (preventionArea) {
                preventionArea.destroy()
                preventionArea = null
            }
        },

        // 远程控制相关方法
        requestAssistance() {
            this.showRequestAssistanceDialog = true
            this.$nextTick(() => {
                this.$refs.requestAssistanceDialog.show()
            })
        },

        startRemoteControl() {
            this.showRemoteControlDialog = true
            this.$nextTick(() => {
                this.$refs.remoteControlDialog.show()
            })
        },

        closeRequestAssistance() {
            this.showRequestAssistanceDialog = false
        },

        closeRemoteControl() {
            this.showRemoteControlDialog = false
        },
    },
    mounted() {
        this.$bus.$on("onChangeViewSync", isViewSyncOpen => {
            this.onChangeViewSync(isViewSyncOpen)
        });
    },
    created() {
        this.$nextTick(() => {
            this.getTimeStr()
            this.timer = setTimeout(this.getTimeStr, 100)
        })
    },
    beforeDestroy() {
        clearInterval(this.timer)
    }
}
</script>
<style lang="scss" scoped>
.user-info {
    margin-left: 20px;
    margin-right: 20px;

    .el-dropdown-link {
        color: #fff;
        cursor: pointer;
        font-size: 14px;
        display: flex;
        align-items: center;

        .avatar-img {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            margin-right: 8px;
            object-fit: cover;
            border: 2px solid rgba(255, 255, 255, 0.5);
        }

        .avatar-icon {
            font-size: 22px;
            margin-right: 8px;
            color: #23b7ea;
        }
    }
}

.dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 远程控制按钮样式 */
.remote-control-buttons {
    display: flex;
    gap: 10px;
    margin-right: 20px;

    .remote-btn {
        border-radius: 4px;
        font-size: 12px;
        padding: 8px 16px;
        border: none;
        transition: all 0.3s ease;

        &.request-btn {
            background: linear-gradient(135deg, #409EFF, #67C23A);
            color: white;

            &:hover {
                background: linear-gradient(135deg, #66B1FF, #85CE61);
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(64, 158, 255, 0.3);
            }
        }

        &.control-btn {
            background: linear-gradient(135deg, #67C23A, #E6A23C);
            color: white;

            &:hover {
                background: linear-gradient(135deg, #85CE61, #EBB563);
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(103, 194, 58, 0.3);
            }
        }
    }
}

.custom-dropDown{
  background:rgba(8, 52, 105, 1);
  border:1px solid #1f72e3;

}
::v-deep{
  .el-dropdown-menu__item{
    color: #fff;
  }
  .el-dropdown-menu__item--divided:before{
  background:rgba(8, 52, 105, 1)
 }
 .el-dropdown-menu__item--divided{
  border-top:1px solid rgba(8, 52, 105, 1);
  margin-top: -4px;
 }
 .el-dropdown-menu__item:hover{
  background:rgb(12, 89, 182);
 }
 .el-dropdown-menu__item.is-active{
  background:rgb(12, 89, 182);
 }
}
</style>
