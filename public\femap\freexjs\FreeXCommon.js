var _0x3ddb=["getView","getZoomForResolution","floor","totalFeatures","ceil","VECTORLINE","publisedProctol","wfs","getServerWfsLayers","/dataServer/queryServerData","WFS","stringify","IconResource","getDataPath","Data/json/iconLibrary.json","_iconMap","loadFile","has","log","set","getIconList","getImageObjformID","get","无法获取图片路径!","removeImageObjfromID","delete","getImageObjformIndex","removeIconFromIndex","loadData","iconList","image","addIconObject","onError","error","ModelResource","Data/json/modelLibrary.json","_modelMap","_modelGroup","addModelObject","该模型ID已存在!","getModelMap","getModelObjformID","getCurModelID","getModelGroup","removeAll","clear","modelGroup","modelList","imageUI","imageUrl","modelUrl","model-type/list-with-model","data","configFile read error","eventId","_line","_titleDom","_content","_root","element","_id","_position","_infoMap","_dragState","_simpleStyle","visible","lodVisible","createBillBoard",'"></div>','<div class="line billBoard-line','""></div>','<div class="img-top"></div>','<div class="content"></div>','<div class="title"><span class="value">标牌<span></div>',"append","style","visibility","hidden","hide","show","remove","_billBoard","getLineElement","top","_offset","atan","abs","pow","transform","deg)","width","setDragState",".billBoard-","draggable","getDragState","setContent","html","appendContent","find",".value","text",".item span.","</label><span class=","<span></div>","setPosition","left","getOffset","setVisible","getVisible","destroy","userAgent","toLowerCase","msie","firefox","FireFox","chrome","Chrome","opera","Opera","Safari","isIE","isSupportWebgl","getBrowserVersion","version","canvas","getContext","webgl2","experimental-webgl2","webgl","experimental-webgl","FeatureDetection","supportsTypedArrays","supportsWebWorkers","windows","isMac","mac","iphone","isWindows","isAndriod","android","isLinux","linux","isPC","isIPhone","isIPad","ipad","angle","isHorizontalScreen","orientation","dedicatedMarkResource","Data/json/dedicatedMark.json","_PNGMap","PNGGroup","SVGGroup","该专用标绘ID已存在!","getSVGMap","_SVGMap","setCurImageID","getPNGformID","模型库中此id无对应模型!","curImageID","curImageType","getSVGformID","getCurImageID","getCurImageType","getDefaultDedicatedMark","values","next","value","svg","removePNGfromID","removeSVGfromID","pngGroup","dedicatedMarkList","svgGroup","OnError","object","exports","function","amd","FreeXCommon","defineProperty","undefined","toStringTag","Module","__esModule","create","default","string","bind","hasOwnProperty","call","getElementsByTagName","exec","FeSDKPath","getSDKPath","indexOf","prototype","constructor","RECEIVER_Earth_ONLY","RECEIVER_MAP_Earth","MAP_VIEW","mapview","addMark","addMarkProcess","DELETE_MARK","deleteMark","UPDATE_MARK","updateMark","CLEAR_MARK","pick","unPick","matchSynchEventType","getTime","idUsed","_callbacks","push","idMap","length","splice","slice","apply","Ids","supportFormat","match","png","jpeg","tiff","mapbox-vector","forEach","featureInfo","zoomLevel","nativeLatLon","split","setvetLayer_","optimalServerName","layerName","thumbResource","updateTime","wmts","url","htc/service/wmts","format","image/","name","@EPSG%3A4326@","wms","type","tms","concat","getServerLayers","http://www.freexgis.com/freexserver/","dataServer/server","ajax","json","application/json","获取图层失败！","/dataServer/queryServerProtocolInfo","then","items","sqrt","scene","camera","getRectangleCameraCoordinates","Rectangle","Cartesian3","magnitude","Free2DMap","EPSG:3857","FeSceneUtil","transExtent4326To3857","getResolutionForExtent"];!function(e,x){!function(x){for(;--x;)e.push(e.shift())}(++x)}(_0x3ddb,176);var _0x332e=function(e,x){return _0x3ddb[e-=0]};!function(e,x){typeof exports===_0x332e("0x0")&&typeof module===_0x332e("0x0")?module[_0x332e("0x1")]=x():typeof define===_0x332e("0x2")&&define[_0x332e("0x3")]?define([],x):typeof exports===_0x332e("0x0")?exports.FreeXCommon=x():e[_0x332e("0x4")]=x()}(window,(function(){return function(e){var x={};function t(i){if(x[i])return x[i][_0x332e("0x1")];var r=x[i]={i:i,l:!1,exports:{}};return e[i].call(r[_0x332e("0x1")],r,r.exports,t),r.l=!0,r[_0x332e("0x1")]}return t.m=e,t.c=x,t.d=function(e,x,i){t.o(e,x)||Object[_0x332e("0x5")](e,x,{enumerable:!0,get:i})},t.r=function(e){typeof Symbol!==_0x332e("0x6")&&Symbol[_0x332e("0x7")]&&Object[_0x332e("0x5")](e,Symbol.toStringTag,{value:_0x332e("0x8")}),Object[_0x332e("0x5")](e,"__esModule",{value:!0})},t.t=function(e,x){if(1&x&&(e=t(e)),8&x)return e;if(4&x&&"object"==typeof e&&e&&e[_0x332e("0x9")])return e;var i=Object[_0x332e("0xa")](null);if(t.r(i),Object.defineProperty(i,_0x332e("0xb"),{enumerable:!0,value:e}),2&x&&typeof e!=_0x332e("0xc"))for(var r in e)t.d(i,r,function(x){return e[x]}[_0x332e("0xd")](null,r));return i},t.n=function(e){var x=e&&e[_0x332e("0x9")]?function(){return e[_0x332e("0xb")]}:function(){return e};return t.d(x,"a",x),x},t.o=function(e,x){return Object.prototype[_0x332e("0xe")][_0x332e("0xf")](e,x)},t.p="",t(t.s=85)}({85:function(e,x,t){t(86),t(87),t(88),t(89),t(90),t(91),t(92),t(93),t(94),e[_0x332e("0x1")]=t(95)},86:function(e,x){var t=void 0;window[_0x332e("0x12")]={getSDKPath:function(){return t||(t=function(){for(var e=/((?:.*\/)|^)FreeXOnline[\w-]*\.js(?:\W|$)/i,x=document[_0x332e("0x10")]("script"),t=0,i=x.length;t<i;++t){var r=x[t].getAttribute("src"),n=e[_0x332e("0x11")](r);if(null!==n)return n[1]}}())},getDataPath:function(e){return t||this[_0x332e("0x13")](),t+e},isWholePath:function(e){return 0==e[_0x332e("0x14")]("/")||0==e.indexOf("http")}}},87:function(e,x){FeInherits=function(e,x){var t=Object[_0x332e("0xa")](x[_0x332e("0x15")]);t[_0x332e("0x16")]=e,e.prototype=t},FeExtend=function(e,x){var t=x.prototype,i=e[_0x332e("0x15")];i.constructor;for(var r in t)i[r]=t[r];i[_0x332e("0x16")]=e}},88:function(e,x){FeSynchEventType=function(){},FeSynchEventType[_0x332e("0x17")]=0,FeSynchEventType.RECEIVER_MAP_ONLY=1,FeSynchEventType[_0x332e("0x18")]=2,FeSynchEventType[_0x332e("0x19")]=_0x332e("0x1a"),FeSynchEventType.ADD_MARK=_0x332e("0x1b"),FeSynchEventType.ADD_MARK_PROCESS=_0x332e("0x1c"),FeSynchEventType[_0x332e("0x1d")]=_0x332e("0x1e"),FeSynchEventType[_0x332e("0x1f")]=_0x332e("0x20"),FeSynchEventType[_0x332e("0x21")]="clearMark",FeSynchEventType.PICK_MARK=_0x332e("0x22"),FeSynchEventType.UNPICK_MARK=_0x332e("0x23"),FeSynchEventType[_0x332e("0x24")]=function(e){for(var x in FeSynchEventType)if(!(FeSynchEventType[x]instanceof Function)&&FeSynchEventType[x]==e)return!0;return!1}},89:function(e,x){FeEvent=function(e){this.type=e,this.timeStamp=(new Date)[_0x332e("0x25")]()},FeSubPub={idUsed:0,idMap:{},subscribe:function(e,x){this[_0x332e("0x26")]+=1;var t=this[_0x332e("0x26")];this[_0x332e("0x27")]||(this[_0x332e("0x27")]={});return(this._callbacks[e]||(this[_0x332e("0x27")][e]=[]))[_0x332e("0x28")]([t,x]),this[_0x332e("0x29")][t]=e,t},unsubscribe:function(e){var x,t,i;if(!(x=this[_0x332e("0x29")][e]))return e;for(delete this[_0x332e("0x29")][e],t=0,i=this[_0x332e("0x27")][x][_0x332e("0x2a")];t<i;t++)if(this[_0x332e("0x27")][x][t][0]==e)return this[_0x332e("0x27")][x][_0x332e("0x2b")](t,1),e},publish:function(){var e=Array[_0x332e("0x15")][_0x332e("0x2c")][_0x332e("0xf")](arguments,0);e[1]instanceof Array||(e[1]=[e[1]]),e[1][_0x332e("0x2b")](0,0,e[0]);var x,t,i,r=(e=e[1])[0],n=new FeEvent(r);if(e[_0x332e("0x2b")](0,1,n),!this[_0x332e("0x27")])return this;if(!(x=this._callbacks[r]))return this;for(t=0,i=x[_0x332e("0x2a")];t<i;t++)x[t][1][_0x332e("0x2d")](this,e);return this}}},90:function(e,x){function t(e){var x=arguments[_0x332e("0x2a")]>1&&void 0!==arguments[1]?arguments[1]:[],t=x.filter((function(x){return x.protocolName.toLocaleLowerCase()===e}))[0],i=t?t[_0x332e("0x2f")]:"";return i[_0x332e("0x30")](_0x332e("0x31"))?_0x332e("0x31"):i[_0x332e("0x30")](_0x332e("0x32"))?_0x332e("0x32"):i[_0x332e("0x30")](_0x332e("0x33"))?_0x332e("0x33"):i[_0x332e("0x30")](_0x332e("0x34"))?_0x332e("0x34"):i.split(";")[0]}function i(e){return e*(Math.PI/180)}function r(e,x,t){var r=[],n=[[],[]],o=x,_=200;return o&&o.items&&o.items instanceof Array&&o[_0x332e("0x53")][_0x332e("0x35")]((function(x,r){if(x[_0x332e("0x36")]){var o,a=x[_0x332e("0x36")][_0x332e("0x63")]||100,c=x[_0x332e("0x36")][_0x332e("0x37")],s=x[_0x332e("0x36")][_0x332e("0x38")],d=[],u=2;switch(s?s[_0x332e("0x39")](";")[_0x332e("0x35")]((function(e){d[_0x332e("0x28")](parseFloat(e))})):d=void 0,x[_0x332e("0x36")].dataType){case 3:o="LANDMARK",_=20,a>2e4&&a<=1e5?u=Math[_0x332e("0x64")](a/2e4)+3:a>1e5&&(u=6+~~(a/35e4));break;case 4:o=_0x332e("0x65"),_=2e3;break;case 5:o="POLYGON",a>1e4?(_=20,u=8):u=0}if(o){var f=function(e,x,t){if(t){if(x<=e)return t;var i;i=[].concat(t);var r=Math[_0x332e("0x54")](e/x),n=[[i[2]+i[0]]/2,[i[3]+i[1]]/2];return i[0]=(i[0]-n[0])*r+n[0],i[2]=(i[2]-n[0])*r+n[0],i[1]=(i[1]-n[1])*r+n[1],i[3]=(i[3]-n[1])*r+n[1],i}}(_,a,d),l=function(e){if(e&&window[_0x332e("0x5b")]){Free2DMap.sceneUtil.getProjection()==_0x332e("0x5c")&&(e=FreeXMap[_0x332e("0x5d")][_0x332e("0x5e")](e));var x=Free2DMap.getView()[_0x332e("0x5f")](e),t=Free2DMap[_0x332e("0x60")]()[_0x332e("0x61")](x);return Math[_0x332e("0x62")](t)}}(f),h=2*function(e,x){if(e&&x&&Cesium){[][_0x332e("0x49")](e);var t=i(e[0]),r=i(e[1]),n=i(e[2]),o=i(e[3]),_=x[_0x332e("0x55")][_0x332e("0x56")][_0x332e("0x57")](new(Cesium[_0x332e("0x58")])(t,r,n,o));return Cesium[_0x332e("0x59")][_0x332e("0x5a")](_)-6378137}}(f,t);h=h<3e3?3e3:h,c=c>1?c:void 0;var p={name:x[_0x332e("0x3c")],thumbResource:x.thumbResource,featureType:o,level:u,extent:d,bbox:d,visibleExtent:f,minZoom:l,format:_0x332e("0x4e"),maxHeight:h},b={},v={},y={},m="setvetLayer_"+FeServerInfo.Ids++;x[_0x332e("0x66")][_0x332e("0x39")](";").forEach((function(x,t){var i=x.toLocaleLowerCase();i==_0x332e("0x67")&&(p.id=m,p[_0x332e("0x47")]="WFS",p[_0x332e("0x40")]=e+_0x332e("0x67")),i==_0x332e("0x3f")&&(b.url=!0),i==_0x332e("0x48")&&(v[_0x332e("0x40")]=!0),i==_0x332e("0x46")&&(y[_0x332e("0x40")]=!0)}));var F=0;switch((b.url||v[_0x332e("0x40")]||y[_0x332e("0x40")])&&(F+=1),F){case 0:n[0][_0x332e("0x28")](p);break;default:n[1][_0x332e("0x28")](p)}}}})),n[_0x332e("0x35")]((function(e){r=r.concat(e)})),r}FeServerInfo=function(){},FeServerInfo[_0x332e("0x2e")]=0,FeServerInfo[_0x332e("0x4a")]=function(e,x){var i=(e=e||_0x332e("0x4b"))+_0x332e("0x4c"),r=new Promise((function(e,x){$[_0x332e("0x4d")]({dataType:_0x332e("0x4e"),url:i,contentType:_0x332e("0x4f"),success:function(x){e(x)},error:function(){x(_0x332e("0x50"))}})})),n=new Promise((function(x,t){$.ajax({dataType:_0x332e("0x4e"),url:e+_0x332e("0x51"),contentType:_0x332e("0x4f"),success:function(e){x(e)},error:function(){t("获取所有协议失败！")}})}));return Promise.all([r,n])[_0x332e("0x52")]((function(x){var i,r,n,o,_=x[0]&&x[0][_0x332e("0x53")]||[],a=x[1]&&x[1][_0x332e("0x53")]||[];return i=e,n=a,o=[],(r=_)instanceof Array&&r[_0x332e("0x35")]((function(e,x){var r,_;if(e[_0x332e("0x36")]){r=(r=e[_0x332e("0x36")][_0x332e("0x37")])>1?r:void 0;var a=e[_0x332e("0x36")][_0x332e("0x38")];_=[],a?a[_0x332e("0x39")](";").forEach((function(e){_[_0x332e("0x28")](parseFloat(e))})):_=void 0}var c=_0x332e("0x3a")+FeServerInfo.Ids++;e[_0x332e("0x3b")]&&e[_0x332e("0x3b")].toLocaleLowerCase&&(e.optimalServerName=e[_0x332e("0x3b")].toLocaleLowerCase());var s={type:e.optimalServerName,name:e[_0x332e("0x3c")],maxVisibleLevel:r,thumbResource:e[_0x332e("0x3d")],id:c,zIndex:1,bbox:_,extent:_,rectangle:_,updateTime:e[_0x332e("0x3e")]};if(e[_0x332e("0x3b")]==_0x332e("0x3f")){if(s[_0x332e("0x40")]=i+_0x332e("0x41"),!(d=t(_0x332e("0x3f"),n)))return;d==_0x332e("0x34")?s.format=d:s[_0x332e("0x42")]=_0x332e("0x43")+d}else if("tms"==e[_0x332e("0x3b")]){if(!(d=t("tms",n))||d==_0x332e("0x34"))return;s[_0x332e("0x42")]=_0x332e("0x43")+d,s[_0x332e("0x40")]=i+"htc/service/tms/1.0.0/"+s[_0x332e("0x44")]+_0x332e("0x45")+d+"/"}else if(e[_0x332e("0x3b")]==_0x332e("0x46")){var d;if(s[_0x332e("0x40")]=i+"wms",!(d=t("wms",n))||d==_0x332e("0x34"))return;s[_0x332e("0x42")]="image/"+d}(s[_0x332e("0x42")]&&s[_0x332e("0x47")]==_0x332e("0x3f")||"wms"==s[_0x332e("0x47")]||s[_0x332e("0x47")]==_0x332e("0x48"))&&(o=o[_0x332e("0x49")](s))})),o}))},FeServerInfo[_0x332e("0x68")]=function(e,x,t){var i=(e=e||_0x332e("0x4b"))+_0x332e("0x69"),n=x||{serverName:"",serverProtocol:_0x332e("0x6a"),serverType:"",serverStatus:"",publishor:"",pageInfo:{pageNum:"",pageSize:"",sidx:"",sord:""}};return new Promise((function(x,o){var _;$[_0x332e("0x4d")]({type:"post",dataType:_0x332e("0x4e"),async:!1,url:i,data:JSON[_0x332e("0x6b")](n),contentType:"application/json",success:function(i){_=r(e,i,t),x(_)},error:function(){o(_0x332e("0x50"))}})}))}},91:function(e,x){window[_0x332e("0x6c")]=void 0,FeIconResource={getIconResource:function(){if(IconResource)return IconResource;var e=FeSDKPath[_0x332e("0x6d")](_0x332e("0x6e"));return IconResource=new FeIconLibrary(e),IconResource}},FeIconLibrary=function(e){this[_0x332e("0x6f")]=new Map,this[_0x332e("0x70")](e)},FeIconLibrary[_0x332e("0x15")].addIconObject=function(e){this[_0x332e("0x6f")][_0x332e("0x71")](e.id)?console[_0x332e("0x72")]("该图片ID已存在!"):this[_0x332e("0x6f")][_0x332e("0x73")](e.id,e)},FeIconLibrary[_0x332e("0x15")][_0x332e("0x74")]=function(){return this[_0x332e("0x6f")]},FeIconLibrary[_0x332e("0x15")][_0x332e("0x75")]=function(e){if(e)return this._iconMap[_0x332e("0x71")](e)?this[_0x332e("0x6f")][_0x332e("0x76")](e):void console[_0x332e("0x72")](_0x332e("0x77"))},FeIconLibrary[_0x332e("0x15")][_0x332e("0x78")]=function(e){this._iconMap&&this[_0x332e("0x6f")][_0x332e("0x79")](entityID)},FeIconLibrary[_0x332e("0x15")][_0x332e("0x7a")]=function(e){if(this[_0x332e("0x6f")][_0x332e("0x2a")]>e){var x=this[_0x332e("0x6f")],t=0;x.forEach((function(x,i){if(t==e)return this[_0x332e("0x6f")].get(iconObj.id);++t}))}},FeIconLibrary[_0x332e("0x15")].getIndexformID=function(e){var x=this[_0x332e("0x6f")],t=0;x[_0x332e("0x35")]((function(x,i){if(i==e)return t;++t}))},FeIconLibrary[_0x332e("0x15")][_0x332e("0x7b")]=function(e){if(this[_0x332e("0x6f")].length>e){var x=this,t=this[_0x332e("0x6f")],i=0;t[_0x332e("0x35")]((function(t,r){i==e&&x[_0x332e("0x6f")].delete(r),++i}))}},FeIconLibrary.prototype.removeAll=function(){var e=this;this[_0x332e("0x6f")].forEach((function(x,t){e[_0x332e("0x6f")].delete(t)}))},FeIconLibrary[_0x332e("0x15")][_0x332e("0x7c")]=function(e){var x=void 0;e&&e.iconList&&(x=e[_0x332e("0x7d")]);var t=this;x[_0x332e("0x35")]((function(e){e[_0x332e("0x7e")]=FeSDKPath[_0x332e("0x13")]()+e[_0x332e("0x7e")],t[_0x332e("0x7f")](e)}))},FeIconLibrary[_0x332e("0x15")].loadFile=function(e){this.removeAll();var x,t=this;null!=e&&(x=e),$[_0x332e("0x4d")]({dataType:"json",async:!1,url:x,success:function(e){e&&t[_0x332e("0x7c")](e)},error:t.OnError})},FeIconLibrary[_0x332e("0x15")][_0x332e("0x80")]=function(e,x,t){console[_0x332e("0x81")]("configFile read error"+x+"\n"+t)}},92:function(e,x){window[_0x332e("0x82")]=void 0,FeModelResource={getModelResource:function(){if(ModelResource)return ModelResource;var e=FeSDKPath[_0x332e("0x6d")](_0x332e("0x83"));return ModelResource=new FeModelLibrary(e),ModelResource}},FeModelLibrary=function(e){this[_0x332e("0x84")]=new Map,this[_0x332e("0x85")]=[],this[_0x332e("0x70")](e)},FeModelLibrary.prototype[_0x332e("0x86")]=function(e){this[_0x332e("0x84")][_0x332e("0x71")](e.id)?console[_0x332e("0x72")](_0x332e("0x87")):this[_0x332e("0x84")].set(e.id,e)},FeModelLibrary[_0x332e("0x15")][_0x332e("0x88")]=function(){return this[_0x332e("0x84")]},FeModelLibrary[_0x332e("0x15")].setCurModelID=function(e){this[_0x332e("0x89")](e)?this.curModelID=e:console[_0x332e("0x72")]("模型库中此id无对应模型!")},FeModelLibrary.prototype[_0x332e("0x8a")]=function(){return this.curModelID},FeModelLibrary[_0x332e("0x15")][_0x332e("0x8b")]=function(){return this._modelGroup},FeModelLibrary[_0x332e("0x15")].getModelObjformID=function(e){return this[_0x332e("0x84")][_0x332e("0x71")](e)?this[_0x332e("0x84")][_0x332e("0x76")](e):void console[_0x332e("0x72")](_0x332e("0x77"))},FeModelLibrary[_0x332e("0x15")].removeModelObjfromID=function(e){this[_0x332e("0x84")]&&this[_0x332e("0x84")].delete(e)},FeModelLibrary.prototype[_0x332e("0x8c")]=function(){this[_0x332e("0x84")];this[_0x332e("0x84")][_0x332e("0x8d")]()},FeModelLibrary.prototype[_0x332e("0x7c")]=function(e){e&&e[_0x332e("0x8e")]&&(this[_0x332e("0x85")]=e[_0x332e("0x8e")]);var x=this;x[_0x332e("0x85")].forEach((function(e){e[_0x332e("0x8f")]&&e[_0x332e("0x8f")][_0x332e("0x35")]((function(e){e[_0x332e("0x90")]=GIS_SERVICES+e[_0x332e("0x90")],e[_0x332e("0x91")]=GIS_SERVICES+e[_0x332e("0x91")],e[_0x332e("0x92")]=GIS_SERVICES+e[_0x332e("0x92")],x.addModelObject(e)}))}))},FeModelLibrary.prototype[_0x332e("0x70")]=function(e){this[_0x332e("0x8c")]();var x=this;$.ajax({dataType:_0x332e("0x4e"),async:!1,url:GIS_SERVICES+_0x332e("0x93"),success:function(e){if(e){var t={modelGroup:e[_0x332e("0x94")]};x[_0x332e("0x7c")](t)}},error:x.OnError})},FeModelLibrary[_0x332e("0x15")][_0x332e("0x80")]=function(e,x,t){console.error(_0x332e("0x95")+x+"\n"+t)}},93:function(e,x){FeBillBoard=function(e){this[_0x332e("0x96")]=0,this._showInfo={},this._billBoard=void 0,this[_0x332e("0x97")]=void 0,this[_0x332e("0x98")]=void 0,this[_0x332e("0x99")]=void 0,this[_0x332e("0x9a")]=e[_0x332e("0x9b")],this[_0x332e("0x9c")]=e.id,this._offset=[45,-25],this[_0x332e("0x9d")]=[-45,25],this[_0x332e("0x9e")]=new Map,this[_0x332e("0x9f")]=!1,this[_0x332e("0xa0")]=!0,this[_0x332e("0xa1")]=e[_0x332e("0xa1")],this[_0x332e("0xa2")]=this.visible,this[_0x332e("0xa3")]()},FeBillBoard[_0x332e("0x15")][_0x332e("0xa3")]=function(){var e=$('<div class="billBoard billBoard-'+this[_0x332e("0x9c")]+_0x332e("0xa4")),x=$(_0x332e("0xa5")+this[_0x332e("0x9c")]+_0x332e("0xa6")),t=($(_0x332e("0xa7")),$(_0x332e("0xa8"))),i=$(_0x332e("0xa9"));e[_0x332e("0xaa")](x)[_0x332e("0xaa")](i)[_0x332e("0xaa")](t),e=e[0],this[_0x332e("0x99")]=t,this._billBoard=e,this._billBoard[_0x332e("0xab")][_0x332e("0xac")]==this[_0x332e("0xa1")]||_0x332e("0xad"),this[_0x332e("0x97")]=x,this._titleDom=i,this[_0x332e("0x9a")]&&this[_0x332e("0x9a")][_0x332e("0xaa")](this._billBoard),this[_0x332e("0x99")].hide();var r=this;$(".billBoard-"+this[_0x332e("0x9c")]).dblclick((function(){r[_0x332e("0xa0")]=!r[_0x332e("0xa0")],r[_0x332e("0xa0")]?r[_0x332e("0x99")][_0x332e("0xae")]():r._content[_0x332e("0xaf")]()}))},FeBillBoard[_0x332e("0x15")][_0x332e("0xb0")]=function(){this._billBoard[_0x332e("0xb0")]()},FeBillBoard[_0x332e("0x15")].getBillBoardElement=function(){return this._billBoard?this[_0x332e("0xb1")]:void 0},FeBillBoard[_0x332e("0x15")][_0x332e("0xb2")]=function(){return this._line?this[_0x332e("0x97")][0]:void 0},FeBillBoard.prototype.move=function(){if(this[_0x332e("0xb1")]){var e=parseInt(this[_0x332e("0xb1")][_0x332e("0xab")].left),x=parseInt(this[_0x332e("0xb1")][_0x332e("0xab")][_0x332e("0xb3")]),t=e-this[_0x332e("0x9d")][0],i=x-this[_0x332e("0x9d")][1];this[_0x332e("0xb4")]=[t,i];var r=Math[_0x332e("0xb5")](Math[_0x332e("0xb6")](i)/Math[_0x332e("0xb6")](t));r=180*r/Math.PI,t>0&&i>0?r=180+r:t<0&&i>0?r=-r:t>0&&i<0&&(r=180-r);var n=Math[_0x332e("0x54")](Math[_0x332e("0xb7")](t,2)+Math.pow(i,2));this._line[0][_0x332e("0xab")][_0x332e("0xb8")]="rotate("+r+_0x332e("0xb9"),this._line[0][_0x332e("0xab")][_0x332e("0xba")]=n+"px"}},FeBillBoard[_0x332e("0x15")][_0x332e("0xbb")]=function(e){if(this._dragState=e,this._dragState){var x=this;$(_0x332e("0xbc")+this[_0x332e("0x9c")])[_0x332e("0xbd")]({drag:function(){x.move()},handle:""})}else{x=this;$(_0x332e("0xbc")+this[_0x332e("0x9c")]).draggable({handle:".billBoard-"+this._id})}},FeBillBoard[_0x332e("0x15")][_0x332e("0xbe")]=function(){return this[_0x332e("0x9f")]},FeBillBoard[_0x332e("0x15")][_0x332e("0xbf")]=function(e){this._infoMap.clear(),this[_0x332e("0x99")][_0x332e("0xc0")](""),this.appendContent(e)},FeBillBoard[_0x332e("0x15")][_0x332e("0xc1")]=function(e){if(e)for(key in e)if(key!=_0x332e("0x44")){var x=e[key];if(this._infoMap[_0x332e("0x71")](key))this[_0x332e("0x99")][_0x332e("0xc2")](_0x332e("0xc5")+key)[_0x332e("0xc4")](x),this._infoMap[_0x332e("0x73")](key,x);else{this._infoMap[_0x332e("0x73")](key,x);var t=$("<div class=item><label>"+key+_0x332e("0xc6")+key+">"+x+_0x332e("0xc7"));this._content[_0x332e("0xaa")](t)}}else this[_0x332e("0x98")][_0x332e("0xc2")](_0x332e("0xc3"))[_0x332e("0xc4")](e[_0x332e("0x44")])},FeBillBoard[_0x332e("0x15")][_0x332e("0xc8")]=function(e){e instanceof Array&&2==e[_0x332e("0x2a")]&&e!=this._position&&(this._position=e,this._billBoard.style[_0x332e("0xb3")]=e[1]+this._offset[1]+"px",this[_0x332e("0xb1")][_0x332e("0xab")][_0x332e("0xc9")]=e[0]+this[_0x332e("0xb4")][0]+"px")},FeBillBoard[_0x332e("0x15")][_0x332e("0xca")]=function(e){return this[_0x332e("0xb4")]},FeBillBoard[_0x332e("0x15")][_0x332e("0xcb")]=function(e){this[_0x332e("0xa1")]!=e&&(e?(this.visible=!0,this[_0x332e("0xb1")][_0x332e("0xab")][_0x332e("0xac")]=_0x332e("0xa1")):(this[_0x332e("0xa1")]=!1,this[_0x332e("0xb1")].style[_0x332e("0xac")]=_0x332e("0xad")))},FeBillBoard[_0x332e("0x15")][_0x332e("0xcc")]=function(){return this.visible},FeBillBoard[_0x332e("0x15")][_0x332e("0xcd")]=function(){}},94:function(e,x){FeDetection=function(){},FeDetection.getBrowserVersion=function(){var e,x=navigator[_0x332e("0xce")][_0x332e("0xcf")](),t={type:"",version:""};return x[_0x332e("0x14")](_0x332e("0xd0"))>=0?t={type:"IE",version:e=Number(x.match(/msie ([\d]+)/)[1])}:x.indexOf(_0x332e("0xd1"))>=0?(e=Number(x.match(/firefox\/([\d]+)/)[1]),t={type:_0x332e("0xd2"),version:e}):x[_0x332e("0x14")](_0x332e("0xd3"))>=0?(e=Number(x.match(/chrome\/([\d]+)/)[1]),t={type:_0x332e("0xd4"),version:e}):x[_0x332e("0x14")](_0x332e("0xd5"))>=0?(e=Number(x[_0x332e("0x30")](/opera.([\d]+)/)[1]),t={type:_0x332e("0xd6"),version:e}):x[_0x332e("0x14")]("")>=0&&(e=Number(x[_0x332e("0x30")](/safari\/([\d]+)/)[1]),t={type:_0x332e("0xd7"),version:e}),t},FeDetection[_0x332e("0xd8")]=function(){return"trident"==navigator.userAgent.toLowerCase()[_0x332e("0x30")](/trident/i)},FeDetection[_0x332e("0xd9")]=function(){var e=FeDetection[_0x332e("0xda")]();if("IE"===e[_0x332e("0x47")]&&e[_0x332e("0xdb")]<11)return!1;try{var x,t=document.createElement(_0x332e("0xdc"));if(typeof WebGL2RenderingContext!==_0x332e("0x6")&&(x=t[_0x332e("0xdd")](_0x332e("0xde"))||t[_0x332e("0xdd")](_0x332e("0xdf"))),x||(x=t.getContext(_0x332e("0xe0"))||t[_0x332e("0xdd")](_0x332e("0xe1"))),!x)return!1}catch(e){return!1}return null==Cesium||Cesium[_0x332e("0xe2")][_0x332e("0xe3")]()&&Cesium[_0x332e("0xe2")][_0x332e("0xe4")]()},FeDetection.isWindows=function(){return navigator[_0x332e("0xce")][_0x332e("0xcf")]().match(/windows/i)==_0x332e("0xe5")},FeDetection[_0x332e("0xe6")]=function(){var e=navigator[_0x332e("0xce")][_0x332e("0xcf")]();e.match(/mac/i),_0x332e("0xe7"),e[_0x332e("0x30")](/iphone/i),_0x332e("0xe8"),e[_0x332e("0x30")](/ipad/i)},FeDetection.isPC=function(){return FeDetection[_0x332e("0xe9")]()||FeDetection[_0x332e("0xe6")]()||FeDetection.isLinux()},FeDetection[_0x332e("0xea")]=function(){return navigator[_0x332e("0xce")].toLowerCase()[_0x332e("0x30")](/android/i)==_0x332e("0xeb")},FeDetection[_0x332e("0xec")]=function(){var e=navigator.userAgent.toLowerCase(),x=e[_0x332e("0x30")](/android/i)==_0x332e("0xeb"),t=e[_0x332e("0x30")](/linux/i)==_0x332e("0xed");return!x&&t},FeDetection[_0x332e("0xee")]=function(){return this[_0x332e("0xe9")]()||this.isLinux()||this.isMac()},FeDetection[_0x332e("0xef")]=function(){return navigator[_0x332e("0xce")][_0x332e("0xcf")]()[_0x332e("0x30")](/iphone/i)==_0x332e("0xe8")},FeDetection[_0x332e("0xf0")]=function(){return navigator.userAgent[_0x332e("0xcf")]()[_0x332e("0x30")](/ipad/i)==_0x332e("0xf1")},FeDetection.isVerticalScreen=function(){return 0==screen.orientation.angle||180==screen.orientation[_0x332e("0xf2")]},FeDetection[_0x332e("0xf3")]=function(){return 90==screen[_0x332e("0xf4")][_0x332e("0xf2")]||-90==screen[_0x332e("0xf4")][_0x332e("0xf2")]}},95:function(e,x){window[_0x332e("0xf5")]=void 0,FeDedicatedMarkResource={getDedicatedMarkResource:function(){if(dedicatedMarkResource)return dedicatedMarkResource;var e=FeSDKPath[_0x332e("0x6d")](_0x332e("0xf6"));return dedicatedMarkResource=new FeDedicatedMarkLibrary(e),dedicatedMarkResource}},FeDedicatedMarkLibrary=function(e){this[_0x332e("0xf7")]=new Map,this._SVGMap=new Map,this[_0x332e("0xf8")]=[],this[_0x332e("0xf9")]=[],this[_0x332e("0x70")](e)},FeDedicatedMarkLibrary[_0x332e("0x15")][_0x332e("0x86")]=function(e){if(e[_0x332e("0x47")]==_0x332e("0x31")){if(this[_0x332e("0xf7")][_0x332e("0x71")](e.id))return void console.log("该专用标绘ID已存在!");this[_0x332e("0xf7")][_0x332e("0x73")](e.id,e)}else{if(this._PNGMap[_0x332e("0x71")](e.id))return void console[_0x332e("0x72")](_0x332e("0xfa"));this._SVGMap[_0x332e("0x73")](e.id,e)}},FeDedicatedMarkLibrary[_0x332e("0x15")].getPNGMap=function(){return this[_0x332e("0xf7")]},FeDedicatedMarkLibrary[_0x332e("0x15")][_0x332e("0xfb")]=function(){return this[_0x332e("0xfc")]},FeDedicatedMarkLibrary[_0x332e("0x15")][_0x332e("0xfd")]=function(e,x){switch(x){case"png":if(!this[_0x332e("0xfe")](e))return void console[_0x332e("0x72")](_0x332e("0xff"));this[_0x332e("0x100")]=e,this[_0x332e("0x101")]=x;break;case"svg":if(!this[_0x332e("0x102")](e))return void console[_0x332e("0x72")]("模型库中此id无对应模型!");this.curImageID=e,this[_0x332e("0x101")]=x}},FeDedicatedMarkLibrary[_0x332e("0x15")][_0x332e("0x103")]=function(){return this[_0x332e("0x100")]},FeDedicatedMarkLibrary[_0x332e("0x15")][_0x332e("0x104")]=function(){return this[_0x332e("0x101")]},FeDedicatedMarkLibrary[_0x332e("0x15")].getDedicatedMark=function(e,x){var t=void 0;switch(x){case _0x332e("0x31"):t=this.getPNGformID(e);break;case"svg":t=this[_0x332e("0x102")](e)}return t},FeDedicatedMarkLibrary[_0x332e("0x15")][_0x332e("0x105")]=function(e){var x=void 0;switch(e){case _0x332e("0x31"):x=this[_0x332e("0xf7")][_0x332e("0x106")]()[_0x332e("0x107")]()[_0x332e("0x108")];break;case _0x332e("0x109"):x=this[_0x332e("0xfc")][_0x332e("0x106")]()[_0x332e("0x107")]()[_0x332e("0x108")]}return x},FeDedicatedMarkLibrary.prototype[_0x332e("0xfe")]=function(e){return this[_0x332e("0xf7")][_0x332e("0x71")](e)?this[_0x332e("0xf7")][_0x332e("0x76")](e):void console[_0x332e("0x72")](_0x332e("0x77"))},FeDedicatedMarkLibrary.prototype.getSVGformID=function(e){return this[_0x332e("0xfc")][_0x332e("0x71")](e)?this[_0x332e("0xfc")][_0x332e("0x76")](e):void console[_0x332e("0x72")](_0x332e("0x77"))},FeDedicatedMarkLibrary[_0x332e("0x15")][_0x332e("0x10a")]=function(e){this[_0x332e("0xf7")]&&this._PNGMap[_0x332e("0x79")](e)},FeDedicatedMarkLibrary.prototype[_0x332e("0x10b")]=function(e){this._SVGMap&&this[_0x332e("0xfc")][_0x332e("0x79")](e)},FeDedicatedMarkLibrary[_0x332e("0x15")][_0x332e("0x8c")]=function(){this[_0x332e("0xf7")];this._PNGMap[_0x332e("0x8d")](),this[_0x332e("0xfc")].clear()},FeDedicatedMarkLibrary.prototype[_0x332e("0x7c")]=function(e){var x;e&&e[_0x332e("0x10c")]&&(this[_0x332e("0xf8")]=e[_0x332e("0x10c")]),(x=this)[_0x332e("0xf8")][_0x332e("0x35")]((function(e){e[_0x332e("0x10d")]&&e.dedicatedMarkList[_0x332e("0x35")]((function(e){e.imageUrl=FeSDKPath[_0x332e("0x13")]()+e.image,x[_0x332e("0x86")](e)}))})),e&&e.svgGroup&&(this[_0x332e("0xf9")]=e[_0x332e("0x10e")]),(x=this)[_0x332e("0xf9")][_0x332e("0x35")]((function(e){e[_0x332e("0x10d")]&&e.dedicatedMarkList[_0x332e("0x35")]((function(e){e[_0x332e("0x91")]=FeSDKPath[_0x332e("0x13")]()+e[_0x332e("0x7e")],x.addModelObject(e)}))}))},FeDedicatedMarkLibrary.prototype[_0x332e("0x70")]=function(e){this.removeAll();var x,t=this;null!=e&&(x=e),$[_0x332e("0x4d")]({dataType:"json",async:!1,url:x,success:function(e){e&&t[_0x332e("0x7c")](e)},error:t[_0x332e("0x10f")]})},FeDedicatedMarkLibrary[_0x332e("0x15")][_0x332e("0x80")]=function(e,x,t){console[_0x332e("0x81")](_0x332e("0x95")+x+"\n"+t)}}})}));