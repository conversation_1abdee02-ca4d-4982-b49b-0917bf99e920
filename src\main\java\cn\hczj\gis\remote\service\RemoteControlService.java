package cn.hczj.gis.remote.service;

import cn.hczj.gis.remote.entity.RemoteControlSession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 远程控制服务
 */
@Service
@Slf4j
public class RemoteControlService {
    
    /**
     * 存储控制会话的Map，key为控制码，value为会话信息
     */
    private final Map<String, RemoteControlSession> sessions = new ConcurrentHashMap<>();
    
    /**
     * 存储用户ID到控制码的映射，用于快速查找用户的会话
     */
    private final Map<String, String> userToControlCode = new ConcurrentHashMap<>();
    
    /**
     * 生成6位数字控制码
     */
    public String generateControlCode(String userId) {
        // 清理该用户之前的会话
        cleanUserSession(userId);
        
        String controlCode;
        int attempts = 0;
        do {
            controlCode = generateRandomCode();
            attempts++;
            if (attempts > 100) {
                throw new RuntimeException("无法生成唯一的控制码");
            }
        } while (sessions.containsKey(controlCode));
        
        // 创建新会话
        RemoteControlSession session = new RemoteControlSession(controlCode, userId);
        sessions.put(controlCode, session);
        userToControlCode.put(userId, controlCode);
        
        log.info("为用户 {} 生成控制码: {}", userId, controlCode);
        return controlCode;
    }
    
    /**
     * 验证控制码并建立连接
     */
    public boolean connectWithControlCode(String controlCode, String controllerUserId) {
        RemoteControlSession session = sessions.get(controlCode);
        
        if (session == null) {
            log.warn("控制码 {} 不存在", controlCode);
            return false;
        }
        
        if (!session.isValid()) {
            log.warn("控制码 {} 已过期或状态无效", controlCode);
            sessions.remove(controlCode);
            userToControlCode.remove(session.getControlledUserId());
            return false;
        }
        
        if ("CONNECTED".equals(session.getStatus())) {
            log.warn("控制码 {} 已被其他用户连接", controlCode);
            return false;
        }
        
        // 建立连接
        session.setControllerUserId(controllerUserId);
        session.setStatus("CONNECTED");
        session.setConnectTime(LocalDateTime.now());
        
        log.info("用户 {} 通过控制码 {} 连接到用户 {}", 
                controllerUserId, controlCode, session.getControlledUserId());
        return true;
    }
    
    /**
     * 断开控制连接
     */
    public void disconnect(String controlCode) {
        RemoteControlSession session = sessions.get(controlCode);
        if (session != null) {
            session.setStatus("DISCONNECTED");
            session.setDisconnectTime(LocalDateTime.now());
            
            log.info("控制码 {} 的连接已断开", controlCode);
            
            // 延迟清理会话
            cleanSessionLater(controlCode);
        }
    }
    
    /**
     * 根据用户ID断开连接
     */
    public void disconnectByUserId(String userId) {
        String controlCode = userToControlCode.get(userId);
        if (controlCode != null) {
            disconnect(controlCode);
        }
    }
    
    /**
     * 获取会话信息
     */
    public RemoteControlSession getSession(String controlCode) {
        return sessions.get(controlCode);
    }
    
    /**
     * 根据用户ID获取会话信息
     */
    public RemoteControlSession getSessionByUserId(String userId) {
        String controlCode = userToControlCode.get(userId);
        return controlCode != null ? sessions.get(controlCode) : null;
    }
    
    /**
     * 清理过期会话
     */
    public void cleanExpiredSessions() {
        sessions.entrySet().removeIf(entry -> {
            RemoteControlSession session = entry.getValue();
            if (session.isExpired()) {
                userToControlCode.remove(session.getControlledUserId());
                log.info("清理过期会话: {}", entry.getKey());
                return true;
            }
            return false;
        });
    }
    
    /**
     * 清理用户的现有会话
     */
    private void cleanUserSession(String userId) {
        String existingControlCode = userToControlCode.get(userId);
        if (existingControlCode != null) {
            sessions.remove(existingControlCode);
            userToControlCode.remove(userId);
            log.info("清理用户 {} 的现有会话: {}", userId, existingControlCode);
        }
    }
    
    /**
     * 生成随机6位数字
     */
    private String generateRandomCode() {
        Random random = new Random();
        return String.format("%06d", random.nextInt(1000000));
    }
    
    /**
     * 延迟清理会话
     */
    private void cleanSessionLater(String controlCode) {
        // 可以使用定时任务或者延迟队列来实现
        // 这里简单处理，立即清理
        RemoteControlSession session = sessions.get(controlCode);
        if (session != null) {
            sessions.remove(controlCode);
            userToControlCode.remove(session.getControlledUserId());
        }
    }
}
