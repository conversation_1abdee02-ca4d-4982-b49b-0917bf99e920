/**
*@import url(./navigator/navigator.css);
*@import url('./fpsCharts.css');
*@import url('./mapScale.css');
*@import url('./satelliteFragmentTip.css');
*@import url('./aerialView.css');
*@import url('./viewerInfomation.css');
*@import url('./billboard.css');
*@import url('./sceneInfo2d.css');
**/



.billBoard {
    top: 0;
    left: 0;
    position: absolute;
    z-index: 0;
    background-color: rgba(14, 13, 13, 0.466);
    text-align: left;
    border:1px solid #fcfcfc;
    box-shadow: 0 0 8px rgb(248, 247, 247);
    border-radius: 8px;
    width: auto;
    height:auto;
    -webkit-user-select: none;
    user-select: none;
}
.billBoard .line {
    top: 0;
    left: 0;
    position: absolute;
    z-index: 0;
    width: 50px;
    border-top: 1px solid #24d1ff;
    transform: rotate(150deg);
    transform-origin: left top;
    margin-bottom: -2px;
}
.billBoard .img-top {
    width: 220px;
    height: 17px;
    background: url('../Data/images/satelliteFragmentTip/top.png');
    background-size: contain;
}
.billBoard .content {
    padding: 0 15px 15px 7px;
}
.billBoard .item {
    margin-top: 2px;
    /* font-size: 12px; */
    font-family: MicrosoftYaHei;
    font-weight: normal;
    font-stretch: normal;
    display: flex;
    justify-items: center;
    /* color: #fff; */
}
.billBoard .content .item .order-icon{
    width: 20px;
    height: 20px;
    display: inline-block;
    background: url('../Data/images/satelliteFragmentTip/dot_normal.png');
}
.billBoard .content .item .order-icon:hover, .billBoard .content .item .order-icon:active {
    width: 20px;
    height: 20px;
    display: inline-block;
    background: url('../Data/images/satelliteFragmentTip/dot_press.png');
}
.billBoard .content .item label {
    /* color: #FF0000; */
    text-align: justify;
    /* width: 60px; */
    margin-right: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.billBoard .content .item .value {
    color: #fff;
}
.billBoard .title {
    margin-top: 5px;
    margin-bottom: 5px;
    margin-left: 5px;
    margin-right: 5px;
    font-size: 14px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    font-stretch: normal;
    display: flex;
    justify-items: center;
    color: #fff;
    white-space: nowrap;
}

.billBoard .title .value {
    /* color: rgba(11, 241, 222, 0.781); */
}
.ol-box {
  box-sizing: border-box;
  border-radius: 2px;
  border: 2px solid blue;
}

.ol-mouse-position {
  top: 8px;
  right: 8px;
  position: absolute;
}

.ol-scale-line {
    /* background: rgba(0,60,136,.3); */
    border-radius: 2px;
    bottom: 2px;
    left: 8px;
    padding: 2px;
    position: absolute;
}
.ol-scale-line-inner {
    border: 1px solid #eee;
    border-top: none;
    color: #eee;
    font-size: 14px;
    text-align: center;
    margin: 1px;
    will-change: contents,width;
    transition: all .25s;
    height: 16px;
    line-height: 12px;
}
.ol-scale-bar {
  position: absolute;
  bottom: 8px;
  left: 8px;
}
.ol-scale-step-marker {
  width: 1px;
  height: 15px;
  background-color: #000000;
  float: right;
  z-Index: 10;
}
.ol-scale-step-text {
  position: absolute;
  bottom: -5px;
  font-size: 12px;
  z-Index: 11;
  color: #000000;
  text-shadow: -2px 0 #FFFFFF, 0 2px #FFFFFF, 2px 0 #FFFFFF, 0 -2px #FFFFFF;
}
.ol-scale-text {
  position: absolute;
  font-size: 14px;
  text-align: center;
  bottom: 25px;
  color: #000000;
  text-shadow: -2px 0 #FFFFFF, 0 2px #FFFFFF, 2px 0 #FFFFFF, 0 -2px #FFFFFF;
}
.ol-scale-singlebar {
  position: relative;
  height: 10px;
  z-Index: 9;
  border: 1px solid black;
}

.ol-unsupported {
  display: none;
}
.ol-viewport, .ol-unselectable {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: rgba(0,0,0,0);
}
.ol-overlaycontainer, .ol-overlaycontainer-stopevent {
  pointer-events: none;
  z-index: 1 !important;
}
.ol-overlaycontainer > *, .ol-overlaycontainer-stopevent > * {
  pointer-events: auto;
}
.ol-selectable {
  -webkit-touch-callout: default;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}
.ol-grabbing {
  cursor: -webkit-grabbing;
  cursor: -moz-grabbing;
  cursor: grabbing;
}
.ol-grab {
  cursor: move;
  cursor: -webkit-grab;
  cursor: -moz-grab;
  cursor: grab;
}
.ol-control {
  position: absolute;
  background-color: rgba(255,255,255,0.4);
  border-radius: 4px;
  padding: 2px;
}
.ol-control:hover {
  background-color: rgba(255,255,255,0.6);
}
.ol-zoom {
  top: .5em;
  left: .5em;
}
.ol-rotate {
  top: .5em;
  right: .5em;
  transition: opacity .25s linear, visibility 0s linear;
}
.ol-rotate.ol-hidden {
  opacity: 0;
  visibility: hidden;
  transition: opacity .25s linear, visibility 0s linear .25s;
}
.ol-zoom-extent {
  top: 4.643em;
  left: .5em;
}
.ol-full-screen {
  right: .5em;
  top: .5em;
}

.ol-control button {
  display: block;
  margin: 1px;
  padding: 0;
  color: white;
  font-size: 1.14em;
  font-weight: bold;
  text-decoration: none;
  text-align: center;
  height: 1.375em;
  width: 1.375em;
  line-height: .4em;
  background-color: rgba(0,60,136,0.5);
  border: none;
  border-radius: 2px;
}
.ol-control button::-moz-focus-inner {
  border: none;
  padding: 0;
}
.ol-control button span {
  pointer-events: none;
}
.ol-zoom-extent button {
  line-height: 1.4em;
}
.ol-compass {
  display: block;
  font-weight: normal;
  font-size: 1.2em;
  will-change: transform;
}
.ol-touch .ol-control button {
  font-size: 1.5em;
}
.ol-touch .ol-zoom-extent {
  top: 5.5em;
}
.ol-control button:hover,
.ol-control button:focus {
  text-decoration: none;
  background-color: rgba(0,60,136,0.7);
}
.ol-zoom .ol-zoom-in {
  border-radius: 2px 2px 0 0;
}
.ol-zoom .ol-zoom-out {
  border-radius: 0 0 2px 2px;
}


.ol-attribution {
  text-align: right;
  bottom: .5em;
  right: .5em;
  max-width: calc(100% - 1.3em);
}

.ol-attribution ul {
  margin: 0;
  padding: 0 .5em;
  color: #000;
  text-shadow: 0 0 2px #fff;
}
.ol-attribution li {
  display: inline;
  list-style: none;
}
.ol-attribution li:not(:last-child):after {
  content: " ";
}
.ol-attribution img {
  max-height: 2em;
  max-width: inherit;
  vertical-align: middle;
}
.ol-attribution ul, .ol-attribution button {
  display: inline-block;
}
.ol-attribution.ol-collapsed ul {
  display: none;
}
.ol-attribution:not(.ol-collapsed) {
  background: rgba(255,255,255,0.8);
}
.ol-attribution.ol-uncollapsible {
  bottom: 0;
  right: 0;
  border-radius: 4px 0 0;
}
.ol-attribution.ol-uncollapsible img {
  margin-top: -.2em;
  max-height: 1.6em;
}
.ol-attribution.ol-uncollapsible button {
  display: none;
}

.ol-zoomslider {
  top: 4.5em;
  left: .5em;
  height: 200px;
}
.ol-zoomslider button {
  position: relative;
  height: 10px;
}

.ol-touch .ol-zoomslider {
  top: 5.5em;
}

.ol-overviewmap {
  left: 0.5em;
  bottom: 0.5em;
}
.ol-overviewmap.ol-uncollapsible {
  bottom: 0;
  left: 0;
  border-radius: 0 4px 0 0;
}
.ol-overviewmap .ol-overviewmap-map,
.ol-overviewmap button {
  display: inline-block;
}
.ol-overviewmap .ol-overviewmap-map {
  border: 1px solid #7b98bc;
  height: 150px;
  margin: 2px;
  width: 150px;
}
.ol-overviewmap:not(.ol-collapsed) button{
  bottom: 1px;
  left: 2px;
  position: absolute;
}
.ol-overviewmap.ol-collapsed .ol-overviewmap-map,
.ol-overviewmap.ol-uncollapsible button {
  display: none;
}
.ol-overviewmap:not(.ol-collapsed) {
  background: rgba(255,255,255,0.8);
}
.ol-overviewmap-box {
  border: 2px dotted rgba(0,60,136,0.7);
}

.ol-overviewmap .ol-overviewmap-box:hover {
  cursor: move;
}

.mousePositionp{
    width: 100%;
    height: 30px;
    /* margin-right: 30px */
}
.eyeSight {
    display: flex;
    justify-content: flex-end; 
    position: absolute;
    bottom: 6px;
    color: #bbb;
    right: 0px;
    z-index: 99999;
    font-size: 14px;
    font-family: 'Microsoft YaHei', sans-serif;
    font-weight: bold;
    
  }
  .eyeSight .sightItem {
    min-width: 150px;
    margin-right: 10px;
    text-align: left;            
  }
  .eyeSight .height {
    min-width: 102px;
  }
/*
* 鹰眼图样式
*/
.hgt_left_up
{
	position:absolute;
	height:19px;
	width:19px;
	left:-5px;
	top:-5px;
	/*background-image:url('./images/left_up.png');*/
   background: url('../Data/images/aerialView/left_up.png')no-repeat center;
}

.hgt_left_down
{
	position:absolute;
	height:19px;
	width:19px;
	left:-5px;
	bottom:-5px;
	/*background-image:url('./images/left_down.png');*/
   background: url('../Data/images/aerialView/left_down.png')no-repeat center;
}

.hgt_right_up
{
	position:absolute;
	height:19px;
	width:19px;
	right:-5px;
	top:-5px;
  /*	background-image:url('./images/right_up.png');*/
    background: url('../Data/images/aerialView/right_up.png')no-repeat center;
}

.hgt_right_down
{
	position:absolute;
	height:19px;
	width:19px;
	right:-5px;
	bottom:-5px;
/*	background-image:url('./images/right_down.png');*/
  background: url('../Data/images/aerialView/right_down.png')no-repeat center;
}
#fpsCharts-frameInfo {
    width: 476px;
    position: absolute;
    right: 20px;
    bottom: 50px;
    padding: 0 12px;
    z-index: 99
}
#fpsCharts-frameInfo .fpsCharts-frameInfo-top {
    display: flex;
    background: url('../Data/images/fpsCharts/background.png');
}
#fpsCharts-frameInfo .fpsCharts-frameInfo-top #fpsCharts-frameInfo-echarts-container {
    height: 174px;
    width: 443px;
}
#fpsCharts-frameInfo .fpsCharts-frameInfo-top #fpsCharts-frameInfo-delete {
    width: 15px;
    height: 15px;
    padding-top: 40px;
    background: url('../Data/images/fpsCharts/off_normal.png') no-repeat center;
}
#fpsCharts-frameInfo .fpsCharts-frameInfo-top #fpsCharts-frameInfo-delete:hover, #fpsCharts-frameInfo .fpsCharts-frameInfo-top #fpsCharts-frameInfo-delete:active {
    width: 15px;
    height: 15px;
    padding-top: 40px;
    background: url('../Data/images/fpsCharts/off_press.png') no-repeat center;
}

#fpsCharts-frameInfo .fpsCharts-frameInfo-bottom {
    display: flex;
}
#fpsCharts-frameInfo .fpsCharts-frameInfo-bottom .item {
    display: flex;
    align-items: center;
}
#fpsCharts-frameInfo .fpsCharts-frameInfo-bottom .bg-max {
    width: 119px;
    height: 72px;
    background: url('../Data/images/fpsCharts/bg_Max.png') no-repeat center;
}
#fpsCharts-frameInfo .fpsCharts-frameInfo-bottom .bg-min {
    width: 119px;
    height: 72px;
    background: url('../Data/images/fpsCharts/bg_Min.png') no-repeat center;
}
#fpsCharts-frameInfo .fpsCharts-frameInfo-bottom .bg-avg {
    width: 121px;
    height: 72px;
    background: url('../Data/images/fpsCharts/bg_Avg.png') no-repeat center;
}
#fpsCharts-frameInfo .fpsCharts-frameInfo-bottom .bg-new {
    width: 119px;
    height: 72px;
    background: url('../Data/images/fpsCharts/bg_New.png') no-repeat center;
}
#fpsCharts-frameInfo .fpsCharts-frameInfo-bottom .max-picture {
    width: 40px;
    height: 40px;
    margin-left: 10px;
    background: url('../Data/images/fpsCharts/Max.png') no-repeat center;
}
#fpsCharts-frameInfo .fpsCharts-frameInfo-bottom .min-picture {
    width: 40px;
    height: 40px;
    margin-left: 10px;
    background: url('../Data/images/fpsCharts/Min.png') no-repeat center;
}
#fpsCharts-frameInfo .fpsCharts-frameInfo-bottom .avg-picture {
    width: 40px;
    height: 40px;
    margin-left: 10px;
    background: url('../Data/images/fpsCharts/Avg.png') no-repeat center;
}
#fpsCharts-frameInfo .fpsCharts-frameInfo-bottom .new-picture {
    width: 40px;
    height: 40px;
    margin-left: 10px;
    background: url('../Data/images/fpsCharts/New.png') no-repeat center;
}
#fpsCharts-frameInfo .fpsCharts-frameInfo-bottom .item .label {
    color: #fff;
    font-size: 14px;
    margin-left: 5px;
}

@media only screen and (max-width: 700px) {
    #fpsCharts-frameInfo {
        width: calc(100% - 24px);
        left: 0;
        bottom: 3.125;
        padding: 0 12px;
    }
    #fpsCharts-frameInfo .fpsCharts-frameInfo-top {
        background-size: cover;
    }
    #fpsCharts-frameInfo .fpsCharts-frameInfo-top #fpsCharts-frameInfo-echarts-container {
        height: 174px;
        width: calc(100% - 29px);
    }
    #fpsCharts-frameInfo .fpsCharts-frameInfo-top #fpsCharts-frameInfo-delete {
        width: 15px;
        height: 15px;
        padding-top: 40px;
        background-size: contain;
    }
    #fpsCharts-frameInfo .fpsCharts-frameInfo-top #fpsCharts-frameInfo-delete:hover, #fpsCharts-frameInfo .fpsCharts-frameInfo-top #fpsCharts-frameInfo-delete:active {
        width: 15px;
        height: 15px;
        padding-top: 40px;
        background-size: contain;
    }
    #fpsCharts-frameInfo .fpsCharts-frameInfo-bottom {
        display: flex;
    }
    #fpsCharts-frameInfo .fpsCharts-frameInfo-bottom .bg-max {
        width: 119px;
        height: 72px;
        background-size: contain;
    }
    #fpsCharts-frameInfo .fpsCharts-frameInfo-bottom .bg-min {
        width: 119px;
        height: 72px;
        background-size: contain;
    }
    #fpsCharts-frameInfo .fpsCharts-frameInfo-bottom .bg-avg {
        width: 121px;
        height: 72px;
        background-size: contain;
    }
    #fpsCharts-frameInfo .fpsCharts-frameInfo-bottom .bg-new {
        width: 119px;
        height: 72px;
        background-size: contain;
    }
    #fpsCharts-frameInfo .fpsCharts-frameInfo-bottom .max-picture {
        width: 40px;
        height: 40px;
        margin-left: 10px;
        background-size: contain;
    }
    #fpsCharts-frameInfo .fpsCharts-frameInfo-bottom .min-picture {
        width: 40px;
        height: 40px;
        margin-left: 10px;
        background-size: contain;
    }
    #fpsCharts-frameInfo .fpsCharts-frameInfo-bottom .avg-picture {
        width: 40px;
        height: 40px;
        margin-left: 10px;
        background-size: contain;
    }
    #fpsCharts-frameInfo .fpsCharts-frameInfo-bottom .new-picture {
        width: 40px;
        height: 40px;
        margin-left: 10px;
        background-size: contain;
    }
    #fpsCharts-frameInfo .fpsCharts-frameInfo-bottom .item .label {
        font-size: 14px;
        margin-left: 5px;
    }
}
@media only screen and (max-width: 370px) {
    #fpsCharts-frameInfo .fpsCharts-frameInfo-bottom {
        flex-wrap: wrap;
    }
}
.mapScale {
  color: white;
    position: absolute;
    bottom: 5px;
    left: 15px;
    z-index: 1;
}
 .mapScale .mapScalebg {
    position: relative;
    border-bottom: 1px solid rgba(255, 255, 255, 0.75);
    width: 80px;
}
.mapScale .mapScalebg .tab {
  position: absolute;
  bottom: 0px;
  height: 5px;
  width: 1px;
  background-color: rgba(255, 255, 255, 0.75);
  color: rgba(255, 255, 255, 0.75);
}
.mapScale .mapScalebg .text {
  position: absolute;
  bottom: 5px;
  right: 20px;
  color: rgba(255, 255, 255, 0.75);
  font-size: 14px;
  width: 50px;
}

.mapScale .mapScaleHide {
 width: 80px;
}

.vhidden {
  visibility: hidden;
}

#satellite-fragment-tip {
    top: 0;
    left: 0;
    position: absolute;
    z-index: 1;
    background-image: linear-gradient(#000613, #000613), linear-gradient(#ffffff, #ffffff);
    background-blend-mode: normal, normal;
    opacity: 0.75;
    text-align: left;
}
#satellite-fragment-tip .line {
    width: 50px;
    border-top: 1px solid #24d1ff;
    transform: rotate(150deg);
    transform-origin: left top;
    margin-bottom: -2px;
}
#satellite-fragment-tip .img-top {
    width: 220px;
    height: 17px;
    background: url('../Data/images/satelliteFragmentTip/top.png');
    background-size: contain;
}
#satellite-fragment-tip .content {
    padding: 0 15px 15px 7px;
}
#satellite-fragment-tip .item {
    margin-top: 10px;
    font-size: 12px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    font-stretch: normal;
    display: flex;
    justify-items: center;
    color: #fff;
}
#satellite-fragment-tip .content .item .order-icon{
    width: 20px;
    height: 20px;
    display: inline-block;
    background: url('../Data/images/satelliteFragmentTip/dot_normal.png');
}
#satellite-fragment-tip .content .item .order-icon:hover, #satellite-fragment-tip .content .item .order-icon:active {
    width: 20px;
    height: 20px;
    display: inline-block;
    background: url('../Data/images/satelliteFragmentTip/dot_press.png');
}
#satellite-fragment-tip .content .item label {
    color: #d3d3d3;
    text-align: justify;
}
#satellite-fragment-tip .content .item .value {
    color: #fff;
}
.eyeSight {
  display: flex;
  justify-content: flex-end; 
  position: absolute;
  bottom: 6px;
  color: #bbb;
  right: 0px;
  z-index: 99999;
  font-size: 14px;
}
.eyeSight .sightItem {
  min-width: 150px;
  margin-right: 10px;
  text-align: left;            
}
.eyeSight .height {
  min-width: 102px;
}
@font-face {font-family: "iconfont";
  src: url('iconfont.eot?t=1563780421172'); /* IE9 */
  src: url('iconfont.eot?t=1563780421172#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('data:application/x-font-woff2;charset=utf-8;base64,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') format('woff2'),
  url('iconfont.woff?t=1563780421172') format('woff'),
  url('iconfont.ttf?t=1563780421172') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+ */
  url('iconfont.svg?t=1563780421172#iconfont') format('svg'); /* iOS 4.1- */
}

.icon-zoomout,
.icon-zoomin,
.icon-fullscreenexit,
.icon-Reset,
.icon-D,
.icon-D1,
.icon-fullscreen,
.icon-d-,
.icon-d-1,
.icon-reset,
.icon-fullscreen-exit-line {
  font-family: "iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-zoomout:before {
  content: "\e625";
}

.icon-zoomin:before {
  content: "\e727";
}

.icon-fullscreenexit:before {
  content: "\e889";
}

.icon-Reset:before {
  content: "\e614";
}

.icon-D:before {
  content: "\e6cc";
}

.icon-D1:before {
  content: "\e6cd";
}

.icon-fullscreen:before {
  content: "\e731";
}

.icon-d-:before {
  content: "\e659";
}

.icon-d-1:before {
  content: "\e65a";
}

.icon-reset:before {
  content: "\e739";
}

.icon-fullscreen-exit-line:before {
  content: "\e6fe";
}

.optionNoAuth {
  cursor: default;
  opacity: 0.3;
  pointer-events: none;
}
.pcNavigator {
	position: absolute;
	bottom: 264px;
  right: 86px;
	z-index: 999;
}
.pcNavigator .compass {
    position: absolute;
    z-index: 999;
    top: 0;
    left: 10px;
    width: 56px;
    height:56px;
    border-radius: 50%;
    background-image: url("../Data/images/navigator/compass_normal.png");
    background-size: cover !important;
    background-repeat: no-repeat;
    background-color: #1b5c94;
 }
 .pcNavigator  .operators {
  display: none;
    position: absolute;
    right: -85px;
    top: -20px;
    width: 46px;
    height: 79px;
    background-image: url("../Data/images/navigator/arc.png");
    background-position: top;
    background-repeat: no-repeat;
 }
.pcNavigator  .operators .operator {
  display: none;
  position: absolute;
  width: 30px;
  height: 30px;
  border-radius: 50%;
}
.pcNavigator  .operators .fullScreen,
.pcNavigator  .operators .unfullScreen {
  top: -24px;
  right: 8px;
  background: url("../Data/images/navigator/fullscreen_normal.png")
	no-repeat center;
}
.pcNavigator .operators .fullScreen {
  background: url("../Data/images/navigator/fullscreen_normal.png")
	no-repeat center;
}
.pcNavigator .operators .fullScreen:hover {
  background: url("../Data/images/navigator/fullscreen_hover.png")
	no-repeat center;
}
.pcNavigator .operators .fullScreen:active {
  background: url("../Data/images/navigator/fullscreen_press.png")
	no-repeat center;
}
.pcNavigator .operators .unfullScreen {
  background: url("../Data/images/navigator/unfullscreen_normal.png")
	no-repeat center;
}
.pcNavigator .operators .unfullScreen:hover {
  background: url("../Data/images/navigator/unfullscreen_hover.png")
	no-repeat center;
}
.pcNavigator .operators .unfullScreen:active {
  background: url("../Data/images/navigator/unfullscreen_press.png")
	no-repeat center;
}
.pcNavigator .operators .reset {
  top: 2px;
  right: -30px;
  background: url("../Data/images/navigator/reset_normal.png")
	no-repeat center;
}
.pcNavigator .operators .reset:hover {
  background: url("../Data/images/navigator/reset_hover.png")
	no-repeat center;
}
.pcNavigator .operators .reset:active {
  background: url("../Data/images/navigator/reset_press.png")
	no-repeat center;
}
.pcNavigator .operators .proj2d,
.pcNavigator .operators .proj3d,
.pcNavigator .operators .projDisable {
  top: 50px;
  right: -35px;
}
.pcNavigator .operators .proj2d {
  background: url("../Data/images/navigator/2D_normal.png")
	no-repeat center;
}
.pcNavigator .operators .proj3d {
  background: url("../Data/images/navigator/3D_normal.png")
	no-repeat center;
}
.pcNavigator .operators .proj2d:hover {
  background: url("../Data/images/navigator/2D_hover.png")
	no-repeat center;
}
.pcNavigator .operators .proj3d:hover {
  background: url("../Data/images/navigator/3D_hover.png")
	no-repeat center;
}
.pcNavigator .operators .proj2d:active {
  background: url("../Data/images/navigator/2D_press.png")
	no-repeat center;
}
.pcNavigator .operators .proj3d:active {
  background: url("../Data/images/navigator/3D_press.png")
	no-repeat center;
}
.pcNavigator .operators .projDisable {
    opacity: 0.3;
    pointer-events: none;
}
.pcNavigator .operators .zoomIn {
  top: 100px;
  right: 4px;
  background: url("../Data/images/navigator/zoomin_normal.png")
	no-repeat center;
}
.pcNavigator .operators .zoomIn:hover {
  background: url("../Data/images/navigator/zoomin_hover.png")
	no-repeat center;
}
.pcNavigator .operators .zoomIn:active {
  background: url("../Data/images/navigator/zoomin_press.png")
	no-repeat center;
}
.pcNavigator .operators .zoomOut {
  top: 100px;
  right: 65px;
  background: url("../Data/images/navigator/zoomout_normal.png")
	no-repeat center;
}
.pcNavigator .operators .zoomOut:hover {
  background: url("../Data/images/navigator/zoomout_hover.png")
	no-repeat center;
}
.pcNavigator .operators .zoomOut:active {
  background: url("../Data/images/navigator/zoomout_press.png")
	no-repeat center;
}

.mobileNavigator {
	position: fixed;
    top: 1rem;
    left: 1rem;
}
.mobileNavigator  .compass {
	position: absolute;
	z-index: 999;
	top: 0;
	left: 0;
	width: 8rem;
	height: 8rem;
	border-radius: 50%;
	background-image: url("../Data/images/navigator/compass_normal.png");
	background-size: cover !important;
	background-repeat: no-repeat;
}
.mobileNavigator  .operator {
position: absolute;
width: 3rem;
height: 3rem;
border-radius: 50%;
}
.mobileNavigator .reset,
.mobileNavigator .zoomIn,
.mobileNavigator .zoomOut {
display: flex;
align-items: center;
justify-content: center;
font-size: 1.5rem;
color: white;
background-color: rgba(255, 255, 255, 0.25);
border-radius: 50%;
}
.mobileNavigator .reset {
 font-size: 1rem;
}
.mobileNavigator .reset {
top: 5rem;
right: -12rem;
}
.mobileNavigator .zoomIn {
top: 9rem;
right: -8rem;
}
.mobileNavigator .zoomOut {
top: 9rem;
right: -3rem;
}
.mobileNavigator .zoomIn:active,
.mobileNavigator .zoomOut:active,
.mobileNavigator .reset:active {
color: #3b7cff;
}


