{"startTime": "2019/08/19 07:59:03", "endTime": "2019/08/19 08:02:00", "sceneCommands": [{"field": "FeMap", "command": "setDepthTest", "parameters": false}, {"field": "FeWeatherManage", "command": "setLightingVisible", "parameters": false}, {"field": "FeWeatherManage", "command": "setCloundVisible", "parameters": false}, {"field": "FeEntitySystem", "command": "loadFile", "parameters": "static/json/entityPath/entity.json"}], "eventCommands": [{"title": "红蓝双方兵势概览", "description": "演习红蓝双方兵力部署及态势概览。", "eventType": 1, "showInList": true, "picture": "", "video": "", "currentTime": "2019/08/19 07:59:03", "duration": 2, "position": [107.86403621404294, 31.93440949179516, 256158.7686010595], "rotation": [357.0625550729448, -53.70659864617062, 0.003695432978150054], "method": [{"field": "FeManipulator", "command": "setDisableState", "parameters": true}]}, {"title": "蓝方兵力部署", "description": "演习蓝方兵力部署情况介绍。", "eventType": 1, "showInList": true, "picture": "", "video": "", "currentTime": "2019/08/19 07:59:04", "duration": 2, "position": [107.0, 33.06, 267158.7686010595], "rotation": [357.0625550729448, -86.70659864617062, 0.003695432978150054], "method": []}, {"title": "蓝方警戒区域", "description": "蓝方警戒区域面积为6702平方公里，内有地面通信雷达、空警2000预警机、歼15战斗机等武器装备，本次演习中用作蓝方的基地。", "eventType": 0, "picture": "", "video": "", "currentTime": "2019/08/19 07:59:04", "duration": 2, "boundingSphere": {"center": [107.0, 33.06, 267158.7686010595]}, "method": []}, {"title": "蓝方J15作战机", "description": "歼15战机是中国参考从苏-33战斗机原型机以国产歼-11战斗机为基础进而研制和发展的单座双发舰载战斗机。歼-15研制由中国航空工业集团公司沈阳飞机工业集团承担。歼-15在属于第四代战斗机改进型，即第四代半战斗机。本次演习任务为飞往红方防御区，摧毁红方地面雷达、作战指控中心，并安全返回蓝方基地。", "eventType": 0, "showInList": true, "picture": "", "video": "", "currentTime": "2019/08/19 07:59:09", "duration": 2, "boundingSphere": {"center": [107.226675, 33.510467, 1989.213016]}, "headingPitchRange": [0.0, -45.0, 50.0]}, {"title": "蓝方KJ2000预警机", "description": "空中指挥预警飞机是拥有整套远程警戒雷达系统，用于搜索、监视空中或海上目标，指挥并可引导己方飞机执行作战任务的飞机。本次演习任务为飞往红方边界区域进行侦查、收集信息，并将信息回传蓝方基地，部署进攻方案。", "eventType": 0, "showInList": true, "picture": "", "video": "", "currentTime": "2019/08/19 07:59:15", "duration": 2, "boundingSphere": {"center": [107.222932, 33.394693, 1324.0]}, "headingPitchRange": [0.0, -45.0, 100.0]}, {"title": "蓝方基站雷达", "description": "地面雷达站是起着侦察、预警、警戒、引导重任，被誉为信息化战争时代的“烽火台”。本次任务为实时监控进入蓝方警戒区域的目标，下达并发送指令给战斗机进行驱赶或摧毁。", "eventType": 0, "showInList": true, "picture": "", "video": "", "currentTime": "2019/08/19 07:59:21", "duration": 2, "boundingSphere": {"center": [107.36605, 33.29422, 909.0]}, "headingPitchRange": [-90.0, -25.0, 50.0]}, {"title": "红方兵力部署", "description": "演习红方兵力部署情况介绍。", "eventType": 1, "showInList": true, "picture": "", "video": "", "currentTime": "2019/08/19 07:59:27", "duration": 2, "position": [108.25, 34.06, 267158.7686010595], "rotation": [357.0625550729448, -86.70659864617062, 0.003695432978150054], "method": []}, {"title": "红方警戒区域", "description": "红方警戒区面积为22071平方公里，内有指控中心、空警2000预警机、歼20歼击机、红旗16防空雷达等武器装备，用于保障地方安全。本次演习主要作为红方防御区域，禁止警戒区域受到任何威胁。", "eventType": 0, "picture": "", "video": "", "currentTime": "2019/08/19 07:59:27", "duration": 2, "boundingSphere": {"center": [108.25, 34.06, 267158.7686010595]}, "method": []}, {"title": "红方KJ2000预警机", "description": "空警2000是中国自主研制的大型、全天候、多传感器空中预警与指挥控制飞机，已装备中国空军部队。主要用于担负空中巡逻警戒、监视、识别、跟踪空中和海上目标，指挥引导中方战机和地面防空武器系统作战等任务，也能配合陆海军协同作战。本次任务主要为搜索、监视威胁目标，接收并下达指令。", "eventType": 0, "showInList": true, "picture": "", "video": "", "currentTime": "2019/08/19 07:59:31", "duration": 2, "boundingSphere": {"center": [108.122911, 34.326737, 511.0]}, "headingPitchRange": [0.0, -45.0, 100.0]}, {"title": "红方编队一J20作战机", "description": "歼20战机是中航工业成都飞机工业集团公司研制的一款具备高隐身性、高态势感知、高机动性等能力的隐形第五代制空战斗机，是用于接替歼-10、歼-11等机型的中国第五代重型战斗机，用于在空中消灭敌机和其他飞航式空袭兵器的军用飞机。本次演习任务为接收指控中心指令，执行驱赶或歼灭威胁目标。", "eventType": 0, "showInList": true, "picture": "", "video": "", "currentTime": "2019/08/19 07:59:37", "duration": 2, "boundingSphere": {"center": [107.722223, 34.348747, 652.0]}, "headingPitchRange": [-90.0, -30.0, 30.0]}, {"title": "红方指挥所", "description": "红方指挥中心，位于红方核心区域内，负责制定红方作战方案，同时是蓝方攻击主要目标。", "eventType": 0, "showInList": true, "picture": "", "video": "", "currentTime": "2019/08/19 07:59:43", "duration": 2, "boundingSphere": {"center": [108.99338, 34.269964, 408.0]}, "headingPitchRange": [-100, -15, 100]}, {"title": "红方红旗16（HQ-16）", "description": "红旗-16防空导弹是中国研制的第三代中低空、中近程地空导弹武器系统。红旗-16导弹系统主要由指挥车、目标搜索雷达、跟踪制导雷达、自行式储运发射车组成。本次任务中使用了红旗-16的目标搜索雷达，实时搜索、监视进入红方防御区的外来目标，准确识别威胁等级，并将信息发送给作战指挥所及预警机。", "eventType": 0, "showInList": true, "picture": "", "video": "", "currentTime": "2019/08/19 07:59:49", "duration": 2, "boundingSphere": {"center": [107.860193, 34.668616, 1085.0]}, "headingPitchRange": [0.5750402117849382, -38.251149269649055, 50.0]}, {"title": "蓝方发起进攻", "description": "红蓝对抗演习开始，蓝方J15战斗机群接收预警机指令，向红方防御区发起进攻。", "eventType": 1, "showInList": true, "picture": "", "video": "", "currentTime": "2019/08/19 07:59:55", "duration": 2, "position": [107.86403621404294, 31.83440949179516, 256158.7686010595], "rotation": [357.0625550729448, -56.70659864617062, 0.003695432978150054], "method": [{"field": "FeManipulator", "command": "setDisableState", "parameters": false}]}, {"title": "红方扫描目标", "description": "红方开启扫描雷达，扫描蓝方目标。", "eventType": 0, "picture": "", "video": "", "currentTime": "2019/08/19 08:00:00", "duration": 0, "position": [107.86403621404294, 31.83440949179516, 256158.7686010595], "rotation": [357.0625550729448, -56.70659864617062, 0.003695432978150054], "method": [{"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "radar01_B", "strEffectID": "test_effect_parabolaRadar_1", "visible": true}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "radar01", "strEffectID": "test_effect_parabolaRadar_1", "visible": true}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "radar02", "strEffectID": "test_effect_parabolaRadar_1", "visible": true}}, {"field": "FeEntitySystem", "command": "setAllEffectVisible", "parameters": {"type": "Ribbon", "visible": true}}]}, {"title": "蓝方预警机开启雷达", "description": "蓝方预警机扫描红方雷达。", "eventType": 0, "picture": "", "video": "", "currentTime": "2019/08/19 08:00:02", "method": [{"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "testY_B", "strEffectID": "test_effect_ringRadar_1", "visible": true}}]}, {"title": "蓝方预警机关闭雷达", "description": "蓝方预警机搜索到红方雷达，关闭搜索雷达。", "eventType": 0, "picture": "", "video": "", "currentTime": "2019/08/19 08:00:08", "method": [{"field": "FeEntitySystem", "command": "setTrackID", "parameters": "test03_B"}, {"unRealTime": true, "field": "FeManipulator", "command": "setType", "parameters": "third"}]}, {"title": "红方发现敌方目标", "description": "红方地面雷达扫描发现不明目标进入防御区，向指控中心及预警机发送指令。", "eventType": 1, "showInList": true, "picture": "", "video": "", "currentTime": "2019/08/19 08:00:20", "duration": 2, "position": [108.04551240238075, 33.137635683306655, 294340.3949133954], "rotation": [2.359198249145713, -77.6549782424523, 0.04517953415130217], "method": [{"unRealTime": true, "field": "FeManipulator", "command": "setType", "parameters": "freedom"}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "radar01", "strEffectID": "test_effect_dynnamicLineEffect_1", "visible": true}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "radar01", "strEffectID": "test_effect_dynnamicLineEffect_2", "visible": true}}]}, {"title": "红方进行战备状态", "description": "红方指控中心及预警机分别向防御战斗机群发送防御指令，战斗机群进入战备状态。", "eventType": 1, "showInList": true, "picture": "", "video": "", "currentTime": "2019/08/19 08:00:28", "duration": 2, "position": [108.24984185437894, 32.62109754000067, 157436.36445048213], "rotation": [3.120995964602593, -39.08583830331753, 0.0005996192364928233], "method": [{"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "testY_R", "strEffectID": "test_effect_dynnamicLineEffect_1", "visible": true}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "testY_R", "strEffectID": "test_effect_dynnamicLineEffect_2", "visible": true}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "testY_R", "strEffectID": "test_effect_dynnamicLineEffect_3", "visible": true}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "ZHS", "strEffectID": "test_effect_dynnamicLineEffect_11", "visible": true}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "ZHS", "strEffectID": "test_effect_dynnamicLineEffect_12", "visible": true}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "ZHS", "strEffectID": "test_effect_dynnamicLineEffect_13", "visible": true}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "radar01", "strEffectID": "test_effect_dynnamicLineEffect_1", "visible": false}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "radar01", "strEffectID": "test_effect_dynnamicLineEffect_2", "visible": false}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "testY_R", "strEffectID": "test_effect_ringRadar_1", "visible": true}}]}, {"title": "红方抵御蓝方", "description": "红方战机起飞，进入备战转态。", "eventType": 0, "picture": "", "video": "", "currentTime": "2019/08/19 08:00:36", "duration": 2, "position": [108.0834609083439, 31.100336037572813, 378294.2169249564], "rotation": [1.4225620513375787, -48.712889601721784, 359.9966907574132], "method": [{"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "testY_R", "strEffectID": "test_effect_dynnamicLineEffect_1", "visible": false}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "testY_R", "strEffectID": "test_effect_dynnamicLineEffect_2", "visible": false}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "testY_R", "strEffectID": "test_effect_dynnamicLineEffect_3", "visible": false}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "ZHS", "strEffectID": "test_effect_dynnamicLineEffect_11", "visible": false}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "ZHS", "strEffectID": "test_effect_dynnamicLineEffect_12", "visible": false}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "ZHS", "strEffectID": "test_effect_dynnamicLineEffect_13", "visible": false}}]}, {"title": "红方发出警戒状态", "description": "蓝方歼击机进入红方警戒区，红方开启警戒状态。", "eventType": 0, "picture": "", "video": "", "currentTime": "2019/08/19 08:00:50", "method": [{"unRealTime": true, "field": "red_warning_line", "command": "setFlashState", "parameters": 1}]}, {"title": "蓝方攻击红方地面雷达", "description": "蓝方战机C执行作战任务，摧毁敌地面雷达。", "eventType": 1, "showInList": true, "picture": "", "video": "", "currentTime": "2019/08/19 08:00:52", "duration": 2, "position": [108.3544500892605, 32.83736592466288, 115738.31596843121], "rotation": [4.554351393905387, -45.762220060047525, 0.0036731946519254523]}, {"title": "蓝方攻击红方地面雷达", "description": "蓝方战机C执行作战任务，摧毁敌地面雷达。", "eventType": 0, "picture": "", "video": "", "currentTime": "2019/08/19 08:00:56", "duration": 2, "position": [108.3544500892605, 32.83736592466288, 115738.31596843121], "rotation": [4.554351393905387, -45.762220060047525, 0.0036731946519254523], "method": [{"field": "FeEntitySystem", "command": "setEntityVisible", "parameters": {"strEntityID": "BlueMissile01", "visible": true}}]}, {"title": "红方成功拦截导弹", "description": "红方地面雷达发现威胁目标，向红方战机下达拦截指令，红方战机发射拦截导弹，拦截成功。", "eventType": 0, "showInList": true, "picture": "", "video": "", "currentTime": "2019/08/19 08:00:58", "method": [{"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "radar01", "strEffectID": "test_effect_dynnamicLineEffect_11", "visible": true}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "radar01", "strEffectID": "test_effect_dynnamicLineEffect_12", "visible": true}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "radar01", "strEffectID": "test_effect_dynnamicLineEffect_13", "visible": true}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "radar01", "strEffectID": "test_effect_aimEffect_1", "visible": true}}]}, {"title": "红方战机拦截导弹", "description": "红方战机发射导弹拦截目标。", "eventType": 0, "picture": "", "video": "", "currentTime": "2019/08/19 08:01:00", "method": [{"field": "FeEntitySystem", "command": "setEntityVisible", "parameters": {"strEntityID": "RedMissile11", "visible": true}}]}, {"title": "红方成功拦截导弹", "description": "红方导弹拦截成功。", "eventType": 1, "picture": "", "video": "", "currentTime": "2019/08/19 08:01:02", "method": [{"field": "FeEntitySystem", "command": "setEntityVisible", "parameters": {"strEntityID": "RedMissile11", "visible": false}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "radar01", "strEffectID": "test_effect_dynnamicLineEffect_11", "visible": false}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "radar01", "strEffectID": "test_effect_dynnamicLineEffect_12", "visible": false}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "radar01", "strEffectID": "test_effect_dynnamicLineEffect_13", "visible": false}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "radar01", "strEffectID": "test_effect_aimEffect_1", "visible": false}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "test01", "strEffectID": "test_effect_aimEffect_1", "visible": true}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "BlueMissile01", "strEffectID": "test_effect_particle_1", "visible": true}}]}, {"title": "红方反击并歼灭敌方", "description": "红方战机锁定蓝方入侵目标，发射导弹将其击毁。", "eventType": 1, "showInList": true, "picture": "", "video": "", "currentTime": "2019/08/19 08:01:04", "duration": 1, "position": [108.07739031453706, 32.87002323282166, 221063.70695931517], "rotation": [4.1698152859193485, -57.863224207013694, 0.003320076530860929], "method": [{"field": "FeEntitySystem", "command": "setEntityVisible", "parameters": {"strEntityID": "BlueMissile01", "visible": false}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "BlueMissile01", "strEffectID": "test_effect_particle_1", "visible": false}}, {"field": "FeEntitySystem", "command": "setEntityVisible", "parameters": {"strEntityID": "RedMissile01", "visible": true}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "test02", "strEffectID": "test_effect_aimEffect_1", "visible": true}}]}, {"title": "红方攻击蓝方歼击机A", "description": "红方发射导弹，并击毁蓝方歼击机A。", "eventType": 0, "picture": "", "video": "", "currentTime": "2019/08/19 08:01:06", "method": [{"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "test01", "strEffectID": "test_effect_aimEffect_1", "visible": false}}, {"field": "FeEntitySystem", "command": "setEntityVisible", "parameters": {"strEntityID": "RedMissile01", "visible": false}}, {"field": "FeEntitySystem", "command": "setEntityVisible", "parameters": {"strEntityID": "test01_B", "visible": true}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "test01_B", "strEffectID": "test_effect_particle_1", "visible": true}}, {"field": "FeEntitySystem", "command": "setEntityVisible", "parameters": {"strEntityID": "RedMissile02", "visible": true}}]}, {"title": "红方攻击蓝方歼击机B", "description": "红方发射导弹，并击毁蓝方歼击机B。", "eventType": 0, "picture": "", "video": "", "currentTime": "2019/08/19 08:01:08", "method": [{"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "test02", "strEffectID": "test_effect_aimEffect_1", "visible": false}}, {"field": "FeEntitySystem", "command": "setEntityVisible", "parameters": {"strEntityID": "RedMissile02", "visible": false}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "test02_B", "strEffectID": "test_effect_particle_1", "visible": true}}, {"field": "FeEntitySystem", "command": "setEntityVisible", "parameters": {"strEntityID": "test01_B", "visible": false}}]}, {"title": "蓝方歼击机坠毁消失", "description": "蓝方歼击机B坠毁消失。", "eventType": 0, "picture": "", "video": "", "currentTime": "2019/08/19 08:01:10", "method": [{"field": "FeEntitySystem", "command": "setEntityVisible", "parameters": {"strEntityID": "test02_B", "visible": false}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "test02_B", "strEffectID": "test_effect_particle_1", "visible": false}}]}, {"title": "红方锁定目标", "description": "红方锁定蓝方目标，并予以跟踪。", "eventType": 0, "picture": "", "video": "", "currentTime": "2019/08/19 08:01:20", "duration": 2, "position": [108.30316865306018, 33.285362707099154, 81704.23715907], "rotation": [4.1698152859193485, -57.8632242070141, 0.003320076530962707], "method": [{"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "test13", "strEffectID": "test_effect_aimEffect_1", "visible": true}}]}, {"title": "红方攻击蓝方歼击机C", "description": "红方发射导弹，并攻击蓝方歼击机C。", "eventType": 0, "picture": "", "video": "", "currentTime": "2019/08/19 08:01:28", "method": [{"field": "FeEntitySystem", "command": "setEntityVisible", "parameters": {"strEntityID": "RedMissile03", "visible": true}}]}, {"title": "红方击毁蓝方歼击机C", "description": "红方击毁蓝方歼击机C。", "eventType": 0, "picture": "", "video": "", "currentTime": "2019/08/19 08:01:30", "duration": 2, "position": [108.10446011159017, 33.83604149268896, 229545.89068610716], "rotation": [3.893014511501813, -87.70709562914021, 0], "method": [{"field": "FeEntitySystem", "command": "setEntityVisible", "parameters": {"strEntityID": "RedMissile03", "visible": false}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "test13", "strEffectID": "test_effect_aimEffect_1", "visible": false}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "test03_B", "strEffectID": "test_effect_particle_1", "visible": true}}]}, {"title": "蓝方歼击机C坠毁消失", "description": "蓝方歼击机C坠毁消失。", "eventType": 0, "picture": "", "video": "", "currentTime": "2019/08/19 08:01:32", "duration": 0, "position": [108.10446011159017, 33.83604149268896, 229545.89068610716], "rotation": [3.893014511501813, -87.70709562914021, 0], "method": [{"field": "FeEntitySystem", "command": "setEntityVisible", "parameters": {"strEntityID": "test03_B", "visible": false}}, {"field": "FeEntitySystem", "command": "setEntityEffectVisible", "parameters": {"strEntityID": "test03_B", "strEffectID": "test_effect_particle_1", "visible": false}}, {"unRealTime": true, "field": "FeManipulator", "command": "setType", "parameters": "freedom"}]}, {"title": "跟踪红方战机", "description": "跟踪红方战机。", "eventType": 1, "picture": "", "video": "", "currentTime": "2019/08/19 08:01:36", "method": [{"field": "FeEntitySystem", "command": "setTrackID", "parameters": "test11"}, {"unRealTime": true, "field": "FeManipulator", "command": "setType", "parameters": "third"}]}, {"title": "推演结束，红方胜利", "description": "推演结束，红方胜利。", "eventType": 1, "showInList": true, "picture": "", "video": "", "currentTime": "2019/08/19 08:01:56", "duration": 2, "position": [108.02875094311968, 33.07290450286041, 194190.8049081417], "rotation": [2.36047714442485, -73.99974709812079, 0.03503926631481462], "method": [{"unRealTime": true, "field": "FeManipulator", "command": "setType", "parameters": "freedom"}]}], "addCommands": [{"field": "FeWmtsLayer", "id": "web_globe_colour", "dataType": "FeDataType.LAYER", "parameters": {"url": "http://www.freexgis.com/freexserver/htc/service/wmts", "name": "web_globe_colour", "format": "image/png", "visible": true, "level": 8, "type": "WMTS"}, "method": [{"field": "web_globe_colour", "command": "setBrightness", "parameters": 0.6}]}, {"field": "FeWall", "id": "red_warning_line", "dataType": "FeDataType.FEATURE", "parameters": {"positions": [[109.29505742512117, 34.22871], [109.29478685645974, 34.25504402757211], [109.2934040367975, 34.28134597118808], [109.29090960835248, 34.3075837859811], [109.28730557379828, 34.333725505215476], [109.28259529892635, 34.35973927923315], [109.27678351361696, 34.38559341425759], [109.26987631109176, 34.4112564110078], [109.26188114542383, 34.4366970030753], [109.25280682728254, 34.46188419501751], [109.24266351789375, 34.48678730012082], [109.23146272119759, 34.51137597778769], [109.21921727418992, 34.53562027050198], [109.2059413354354, 34.55949064032757], [109.19165037174388, 34.58295800489577], [109.17636114300474, 34.60599377283773], [109.1600916851773, 34.62856987861859], [109.14286129143858, 34.65065881673105], [109.12469049149382, 34.67223367520656], [109.10560102905833, 34.693268168403414], [109.08561583752338, 34.71373666903177], [109.06475901382261, 34.733614239376536], [109.04305579051959, 34.752876661680155], [109.02053250614071, 34.77150046764821], [108.9972165737823, 34.78946296704189], [108.97313644802446, 34.806742275322605], [108.9483215901885, 34.823317340314894], [108.92280243197871, 34.83916796785524], [108.89661033755375, 34.8542748463956], [108.86977756407633, 34.8686195705315], [108.84233722079445, 34.88218466342622], [108.81432322671102, 34.89495359810361], [108.78577026690273, 34.90691081758367], [108.75671374755241, 34.91804175383634], [108.72718974976323, 34.92833284553037], [108.69723498222585, 34.93777155455576], [108.66688673281372, 34.946346381299485], [108.63618281918379, 34.954046878656], [108.6051615384639, 34.960863664755465], [108.57386161610985, 34.96678843439405], [108.54232215401821, 34.971813969152585], [108.51058257798283, 34.9759341461911], [108.47868258458506, 34.97914394570854], [108.4466620876096, 34.98143945705862], [108.41456116407878, 34.982817883514365], [108.38242, 34.983277545675456], [108.35027883592122, 34.982817883514365], [108.3181779123904, 34.98143945705862], [108.28615741541493, 34.97914394570854], [108.25425742201716, 34.9759341461911], [108.22251784598178, 34.971813969152585], [108.19097838389014, 34.96678843439405], [108.15967846153609, 34.960863664755465], [108.1286571808162, 34.954046878656], [108.09795326718627, 34.946346381299485], [108.06760501777414, 34.93777155455576], [108.03765025023677, 34.92833284553037], [108.00812625244758, 34.91804175383634], [107.97906973309726, 34.90691081758367], [107.95051677328897, 34.89495359810361], [107.92250277920554, 34.88218466342622], [107.89506243592366, 34.8686195705315], [107.86822966244624, 34.8542748463956], [107.84203756802128, 34.83916796785524], [107.8165184098115, 34.823317340314894], [107.79170355197553, 34.806742275322605], [107.7676234262177, 34.78946296704189], [107.74430749385928, 34.77150046764821], [107.7217842094804, 34.752876661680155], [107.70008098617738, 34.733614239376536], [107.67922416247663, 34.71373666903177], [107.65923897094166, 34.693268168403414], [107.64014950850617, 34.67223367520656], [107.62197870856141, 34.65065881673105], [107.60474831482269, 34.62856987861859], [107.58847885699525, 34.60599377283773], [107.57318962825612, 34.58295800489577], [107.5588986645646, 34.55949064032757], [107.54562272581008, 34.53562027050198], [107.5333772788024, 34.51137597778769], [107.52217648210625, 34.48678730012082], [107.51203317271745, 34.46188419501751], [107.50295885457616, 34.4366970030753], [107.49496368890823, 34.4112564110078], [107.48805648638303, 34.38559341425759], [107.48224470107364, 34.35973927923315], [107.47753442620171, 34.333725505215476], [107.47393039164751, 34.3075837859811], [107.4714359632025, 34.28134597118808], [107.47005314354026, 34.25504402757211], [107.46978257487882, 34.22871], [107.47062354330596, 34.20237597242789], [107.47257398473691, 34.17607402881192], [107.47563049247255, 34.1498362140189], [107.47978832632465, 34.12369449478452], [107.48504142327334, 34.097680720766846], [107.49138240962071, 34.07182658574241], [107.49880261460392, 34.04616358899221], [107.50729208542953, 34.0207229969247], [107.516839603691, 33.99553580498249], [107.52743270312965, 33.97063269987918], [107.53905768869987, 33.94604402221231], [107.55169965689818, 33.92179972949802], [107.565342517316, 33.89792935967243], [107.57996901537568, 33.87446199510423], [107.59556075620914, 33.85142622716227], [107.6120982296385, 33.82885012138141], [107.6295608362186, 33.80676118326895], [107.64792691430057, 33.785186324793436], [107.66717376807698, 33.764151831596585], [107.68727769656834, 33.74368333096823], [107.70821402351173, 33.72380576062346], [107.72995712811233, 33.704543338319844], [107.7524804766195, 33.68591953235179], [107.77575665468868, 33.66795703295811], [107.799757400492, 33.650677724677394], [107.82445363853975, 33.634102659685105], [107.84981551417633, 33.61825203214476], [107.8758124287143, 33.6031451536044], [107.90241307517073, 33.5888004294685], [107.92958547457083, 33.575235336573776], [107.95729701278391, 33.56246640189639], [107.98551447785749, 33.55050918241633], [108.01420409781602, 33.53937824616366], [108.04333157889066, 33.52908715446963], [108.0728621441475, 33.51964844544424], [108.10276057248173, 33.511073618700514], [108.13299123794573, 33.503373121344], [108.16351814937967, 33.496556335244534], [108.1943049903131, 33.49063156560595], [108.22531515910671, 33.485606030847414], [108.2565118093036, 33.4814858538089], [108.28785789015964, 33.478276054291456], [108.31931618732277, 33.47598054294138], [108.35084936363106, 33.474602116485634], [108.38242, 33.47414245432454], [108.41399063636894, 33.474602116485634], [108.44552381267722, 33.47598054294138], [108.47698210984035, 33.478276054291456], [108.50832819069639, 33.4814858538089], [108.53952484089328, 33.485606030847414], [108.57053500968689, 33.49063156560595], [108.60132185062032, 33.496556335244534], [108.63184876205428, 33.503373121344], [108.66207942751826, 33.511073618700514], [108.69197785585249, 33.51964844544424], [108.72150842110933, 33.52908715446963], [108.75063590218397, 33.53937824616366], [108.7793255221425, 33.55050918241633], [108.80754298721608, 33.56246640189639], [108.83525452542916, 33.575235336573776], [108.86242692482926, 33.5888004294685], [108.8890275712857, 33.6031451536044], [108.91502448582366, 33.61825203214476], [108.94038636146024, 33.634102659685105], [108.965082599508, 33.650677724677394], [108.98908334531131, 33.66795703295811], [109.0123595233805, 33.68591953235179], [109.03488287188766, 33.704543338319844], [109.05662597648826, 33.72380576062346], [109.07756230343165, 33.74368333096823], [109.09766623192301, 33.764151831596585], [109.11691308569942, 33.785186324793436], [109.13527916378139, 33.80676118326895], [109.15274177036149, 33.82885012138141], [109.16927924379085, 33.85142622716227], [109.18487098462431, 33.87446199510423], [109.199497482684, 33.89792935967243], [109.21314034310181, 33.92179972949802], [109.22578231130012, 33.94604402221231], [109.23740729687034, 33.97063269987918], [109.248000396309, 33.99553580498249], [109.25754791457047, 34.0207229969247], [109.26603738539607, 34.0461635889922], [109.27345759037928, 34.07182658574241], [109.27979857672665, 34.097680720766846], [109.28505167367534, 34.12369449478452], [109.28920950752745, 34.1498362140189], [109.29226601526308, 34.17607402881192], [109.29421645669403, 34.20237597242789], [109.29505742512117, 34.22871]], "maximumHeights": [10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000], "minimumHeights": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "fillElevation": [0, 0.5], "alpha": 0.7, "color": ["rgb(110,126,55)", "rgb(174, 56, 36)"], "fill": true}}, {"field": "FeWall", "id": "blue_warning_line", "dataType": "FeDataType.FEATURE", "parameters": {"positions": [[107.54358608564485, 33.18537], [107.54336570362084, 33.19988135584246], [107.54254003017824, 33.21437503183312], [107.54110977131235, 33.228833369660386], [107.53907637189836, 33.24323875406674], [107.53644201539035, 33.257573634310255], [107.5332096226059, 33.27182054554748], [107.52938284959072, 33.28596213011164], [107.52496608455877, 33.299981158660295], [107.51996444390468, 33.31386055116661], [107.5143837672862, 33.32758339772879], [107.50823061177634, 33.34113297917213], [107.50151224508551, 33.35449278741879], [107.49423663785589, 33.36764654560035], [107.48641245503133, 33.38057822788865], [107.47804904630775, 33.39327207902079], [107.4691564356702, 33.4057126334945], [107.45974531002467, 33.41788473441048], [107.44982700693365, 33.42977355193873], [107.43941350146642, 33.44136460138642], [107.42851739217669, 33.45264376084527], [107.41715188622089, 33.4635972883969], [107.40533078363329, 33.47421183885524], [107.39306846077436, 33.48447448002561], [107.38037985297125, 33.49437270846058], [107.36728043637028, 33.503894464693516], [107.35378620902311, 33.51302814793115], [107.33991367122952, 33.5217626301874], [107.32567980516141, 33.53008726984108], [107.31110205379407, 33.53799192460109], [107.29619829917175, 33.545466963863205], [107.28098684003662, 33.552503280443545], [107.26548636885096, 33.55909230167421], [107.2497159482438, 33.56522599984779], [107.23369498691477, 33.57089690199789], [107.2174432150285, 33.57609809900377], [107.20098065913463, 33.58082325400807], [107.18432761664901, 33.585066610137254], [107.16750462993294, 33.588822997515535], [107.15053246000788, 33.59208783956351], [107.13343205994437, 33.59485715857406], [107.11622454796391, 33.59712758055855], [107.09893118029392, 33.59889633935752], [107.08157332381586, 33.60016128001083], [107.06417242854745, 33.60092086138315], [107.04675, 33.60117415804158], [107.02932757145255, 33.60092086138315], [107.01192667618415, 33.60016128001083], [106.99456881970609, 33.59889633935752], [106.9772754520361, 33.59712758055855], [106.96006794005564, 33.59485715857406], [106.94296753999213, 33.59208783956351], [106.92599537006707, 33.588822997515535], [106.90917238335099, 33.585066610137254], [106.89251934086538, 33.58082325400807], [106.87605678497151, 33.57609809900377], [106.85980501308524, 33.57089690199789], [106.8437840517562, 33.56522599984779], [106.82801363114905, 33.55909230167421], [106.81251315996339, 33.552503280443545], [106.79730170082826, 33.545466963863205], [106.78239794620593, 33.53799192460109], [106.76782019483859, 33.53008726984108], [106.75358632877048, 33.5217626301874], [106.7397137909769, 33.51302814793115], [106.72621956362973, 33.503894464693516], [106.71312014702876, 33.49437270846058], [106.70043153922565, 33.48447448002561], [106.68816921636672, 33.47421183885524], [106.67634811377911, 33.4635972883969], [106.66498260782332, 33.45264376084527], [106.65408649853359, 33.44136460138642], [106.64367299306636, 33.42977355193873], [106.63375468997533, 33.41788473441048], [106.6243435643298, 33.4057126334945], [106.61545095369226, 33.39327207902079], [106.60708754496868, 33.38057822788865], [106.59926336214411, 33.36764654560035], [106.5919877549145, 33.35449278741879], [106.58526938822367, 33.34113297917213], [106.5791162327138, 33.32758339772879], [106.57353555609532, 33.31386055116661], [106.56853391544124, 33.299981158660295], [106.56411715040929, 33.28596213011164], [106.56029037739411, 33.27182054554748], [106.55705798460966, 33.257573634310255], [106.55442362810165, 33.24323875406674], [106.55239022868766, 33.228833369660386], [106.55095996982176, 33.21437503183312], [106.55013429637917, 33.19988135584246], [106.54991391435516, 33.18537], [106.55029879147153, 33.17085864415754], [106.55128815868244, 33.156364968166876], [106.55288051257101, 33.14190663033961], [106.55507361862625, 33.12750124593326], [106.55786451538978, 33.11316636568974], [106.56124951946012, 33.09891945445252], [106.56522423134204, 33.084777869888356], [106.56978354212737, 33.0707588413397], [106.57492164099277, 33.056879448833385], [106.58063202349966, 33.04315660227121], [106.58690750068025, 33.02960702082787], [106.5937402088936, 33.016247212581206], [106.60112162043434, 33.00309345439965], [106.60904255487681, 32.99016177211135], [106.6174931911363, 32.97746792097921], [106.62646308022904, 32.965027366505495], [106.6359411587116, 32.95285526558952], [106.64591576278069, 32.94096644806127], [106.65637464301321, 32.92937539861358], [106.66730497972661, 32.91809623915473], [106.67869339893903, 32.9071427116031], [106.69052598890853, 32.89652816114476], [106.7027883172303, 32.88626551997439], [106.7154654484709, 32.87636729153942], [106.72854196231768, 32.86684553530648], [106.74200197222231, 32.857711852068846], [106.75582914451613, 32.848977369812594], [106.77000671797575, 32.840652730158915], [106.78451752381677, 32.83274807539891], [106.79934400609348, 32.82527303613679], [106.8144682424822, 32.81823671955645], [106.8298719654262, 32.811647698325785], [106.84553658361945, 32.80551400015221], [106.86144320380727, 32.79984309800211], [106.87757265288076, 32.794641900996226], [106.89390550024306, 32.78991674599193], [106.91042208042452, 32.785673389862744], [106.92710251592436, 32.78191700248446], [106.94392674025615, 32.778652160436486], [106.96087452117449, 32.77588284142594], [106.97792548406035, 32.77361241944145], [106.99505913544226, 32.771843660642475], [107.01225488663086, 32.77057871998917], [107.029492077444, 32.769819138616846], [107.04675, 32.76956584195842], [107.064007922556, 32.769819138616846], [107.08124511336915, 32.77057871998916], [107.09844086455774, 32.771843660642475], [107.11557451593966, 32.77361241944145], [107.13262547882552, 32.77588284142594], [107.14957325974386, 32.778652160436486], [107.16639748407565, 32.78191700248446], [107.18307791957548, 32.785673389862744], [107.19959449975694, 32.78991674599193], [107.21592734711925, 32.794641900996226], [107.23205679619274, 32.79984309800211], [107.24796341638056, 32.80551400015221], [107.26362803457381, 32.811647698325785], [107.2790317575178, 32.81823671955645], [107.29415599390653, 32.82527303613679], [107.30898247618323, 32.83274807539891], [107.32349328202426, 32.840652730158915], [107.33767085548388, 32.848977369812594], [107.3514980277777, 32.857711852068846], [107.36495803768233, 32.86684553530648], [107.37803455152911, 32.87636729153942], [107.3907116827697, 32.88626551997439], [107.40297401109147, 32.89652816114476], [107.41480660106097, 32.9071427116031], [107.4261950202734, 32.91809623915473], [107.4371253569868, 32.92937539861358], [107.44758423721932, 32.94096644806127], [107.4575588412884, 32.95285526558952], [107.46703691977096, 32.965027366505495], [107.4760068088637, 32.97746792097921], [107.4844574451232, 32.99016177211135], [107.49237837956566, 33.00309345439965], [107.4997597911064, 33.016247212581206], [107.50659249931975, 33.02960702082787], [107.51286797650035, 33.04315660227121], [107.51857835900724, 33.056879448833385], [107.52371645787264, 33.0707588413397], [107.52827576865796, 33.084777869888356], [107.53225048053989, 33.09891945445252], [107.53563548461022, 33.11316636568974], [107.53842638137375, 33.12750124593326], [107.540619487429, 33.14190663033961], [107.54221184131757, 33.156364968166876], [107.54320120852847, 33.17085864415754], [107.54358608564485, 33.18537]], "maximumHeights": [10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000, 10000], "minimumHeights": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "fillElevation": [0, 0.5], "alpha": 0.7, "color": ["rgb(110,126,55)", "rgb(174, 56, 36)"], "fill": true}}]}