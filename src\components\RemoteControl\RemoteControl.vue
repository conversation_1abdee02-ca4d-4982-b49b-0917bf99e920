<template>
    <el-dialog
        title="远程控制"
        :visible.sync="visible"
        width="450px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="handleClose"
        custom-class="remote-control-dialog">

        <!-- 输入控制码状态 -->
        <div v-if="status === 'input'" class="input-container">
            <div class="input-section">
                <h3>请输入控制码</h3>
                <el-input
                    v-model="inputControlCode"
                    placeholder="请输入6位数字控制码"
                    maxlength="6"
                    show-word-limit
                    size="large"
                    class="control-code-input"
                    @keyup.enter="connectToTarget">
                    <template slot="prepend">控制码</template>
                </el-input>
                <p class="input-tip">请向被控制方获取6位数字控制码</p>
            </div>
        </div>

        <!-- 连接中状态 -->
        <div v-else-if="status === 'connecting'" class="status-container">
            <i class="el-icon-loading loading-icon"></i>
            <p>正在连接到目标设备...</p>
        </div>

        <!-- 已连接状态 -->
        <div v-else-if="status === 'connected'" class="connected-container">
            <div class="status-info">
                <i class="el-icon-success status-icon connected"></i>
                <span>远程控制已建立</span>
            </div>

            <div class="target-info">
                <p>目标用户：{{ controlledUserId }}</p>
                <p>连接时间：{{ connectTime }}</p>
                <p>控制码：{{ currentControlCode }}</p>
            </div>

            <div class="control-panel">
                <h4>控制面板</h4>
                <div class="control-buttons">
                    <el-button
                        type="primary"
                        icon="el-icon-zoom-in"
                        @click="sendControlCommand('ZOOM_IN')"
                        size="small">
                        放大地图
                    </el-button>
                    <el-button
                        type="primary"
                        icon="el-icon-zoom-out"
                        @click="sendControlCommand('ZOOM_OUT')"
                        size="small">
                        缩小地图
                    </el-button>
                    <el-button
                        type="primary"
                        icon="el-icon-refresh"
                        @click="sendControlCommand('RESET_VIEW')"
                        size="small">
                        复位视角
                    </el-button>
                    <el-button
                        type="primary"
                        icon="el-icon-location"
                        @click="sendControlCommand('RESET_NORTH')"
                        size="small">
                        指北针
                    </el-button>
                </div>
            </div>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="status === 'error'" class="error-container">
            <i class="el-icon-error status-icon error"></i>
            <p>{{ errorMessage }}</p>
        </div>

        <div slot="footer" class="dialog-footer">
            <el-button
                v-if="status === 'input'"
                type="primary"
                @click="connectToTarget"
                :disabled="!inputControlCode || inputControlCode.length !== 6">
                连接
            </el-button>
            <el-button
                v-if="status === 'connected'"
                type="danger"
                @click="disconnectControl">
                断开控制
            </el-button>
            <el-button @click="handleClose">关闭</el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    name: 'RemoteControl',
    data() {
        return {
            visible: false,
            status: 'input', // input, connecting, connected, error
            inputControlCode: '',
            currentControlCode: '',
            controlledUserId: '',
            connectTime: '',
            errorMessage: '',
            websocket: null,
        }
    },
    methods: {
        show() {
            this.visible = true
            this.status = 'input'
            this.inputControlCode = ''
            this.currentControlCode = ''
            this.controlledUserId = ''
            this.connectTime = ''
            this.errorMessage = ''
            this.connectWebSocket()
        },

        hide() {
            this.visible = false
            this.disconnectWebSocket()
        },

        handleClose() {
            this.hide()
            this.$emit('close')
        },

        connectToTarget() {
            if (!this.inputControlCode || this.inputControlCode.length !== 6) {
                this.$message.warning('请输入6位数字控制码')
                return
            }

            this.status = 'connecting'

            if (this.websocket) {
                this.websocket.send(JSON.stringify({
                    type: 'CONNECT_WITH_CODE',
                    controlCode: this.inputControlCode
                }))
            }
        },

        disconnectControl() {
            if (this.websocket) {
                this.websocket.send(JSON.stringify({
                    type: 'DISCONNECT'
                }))
            }
            this.status = 'input'
            this.currentControlCode = ''
            this.controlledUserId = ''
            this.connectTime = ''
        },

        sendControlCommand(command) {
            if (this.websocket && this.status === 'connected') {
                this.websocket.send(JSON.stringify({
                    type: 'CONTROL_COMMAND',
                    data: {
                        command: command,
                        timestamp: Date.now()
                    }
                }))

                this.$message.success(`已发送${this.getCommandName(command)}命令`)
            }
        },

        getCommandName(command) {
            const commandNames = {
                'ZOOM_IN': '放大地图',
                'ZOOM_OUT': '缩小地图',
                'RESET_VIEW': '复位视角',
                'RESET_NORTH': '指北针'
            }
            return commandNames[command] || command
        },

        connectWebSocket() {
            // 获取当前用户ID（这里需要根据实际情况获取）
            const userId = this.$store.getters['auth/user']?.id || 'controller_' + Date.now()
            const wsUrl = `${REMOTE_CONTROL_SERVICES}/${userId}`

            try {
                this.websocket = new WebSocket(wsUrl)

                this.websocket.onopen = () => {
                    console.log('远程控制WebSocket连接成功')
                }

                this.websocket.onmessage = (event) => {
                    this.handleWebSocketMessage(JSON.parse(event.data))
                }

                this.websocket.onerror = (error) => {
                    console.error('远程控制WebSocket错误:', error)
                    this.status = 'error'
                    this.errorMessage = '连接服务器失败'
                }

                this.websocket.onclose = () => {
                    console.log('远程控制WebSocket连接关闭')
                }
            } catch (error) {
                console.error('创建WebSocket连接失败:', error)
                this.status = 'error'
                this.errorMessage = '无法连接到服务器'
            }
        },

        disconnectWebSocket() {
            if (this.websocket) {
                this.websocket.close()
                this.websocket = null
            }
        },

        handleWebSocketMessage(message) {
            console.log('收到WebSocket消息:', message)

            switch (message.type) {
                case 'CONTROL_CONNECTED':
                    this.currentControlCode = message.data.controlCode
                    this.controlledUserId = message.data.controlledUserId
                    this.connectTime = new Date().toLocaleString()
                    this.status = 'connected'
                    this.$message.success('远程控制连接成功')
                    break

                case 'CONTROL_DISCONNECTED':
                    this.status = 'input'
                    this.currentControlCode = ''
                    this.controlledUserId = ''
                    this.connectTime = ''
                    this.$message.info('远程控制已断开')
                    break

                case 'ERROR':
                    this.status = 'error'
                    this.errorMessage = message.message
                    this.$message.error(message.message)
                    break
            }
        }
    },

    beforeDestroy() {
        this.disconnectWebSocket()
    }
}
</script>

<style lang="scss" scoped>
.remote-control-dialog {
    .input-container {
        .input-section {
            text-align: center;

            h3 {
                margin-bottom: 20px;
                color: #303133;
            }

            .control-code-input {
                margin-bottom: 15px;

                ::v-deep .el-input__inner {
                    text-align: center;
                    font-size: 18px;
                    font-weight: bold;
                    letter-spacing: 2px;
                }
            }

            .input-tip {
                color: #909399;
                font-size: 12px;
            }
        }
    }

    .status-container {
        text-align: center;
        padding: 20px;

        .loading-icon {
            font-size: 24px;
            color: #409EFF;
            margin-bottom: 10px;
        }
    }

    .connected-container {
        .status-info {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;

            .status-icon.connected {
                color: #67C23A;
                margin-right: 8px;
                font-size: 16px;
            }
        }

        .target-info {
            background: #f0f9ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;

            p {
                margin: 5px 0;
                color: #606266;
            }
        }

        .control-panel {
            h4 {
                margin-bottom: 15px;
                color: #303133;
            }

            .control-buttons {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 10px;

                .el-button {
                    width: 100%;
                }
            }
        }
    }

    .error-container {
        text-align: center;
        padding: 20px;

        .status-icon.error {
            font-size: 24px;
            color: #F56C6C;
            margin-bottom: 10px;
        }

        p {
            color: #F56C6C;
        }
    }
}
</style>
