<template>
    <el-dialog
        title="远程控制"
        :visible.sync="visible"
        width="450px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="handleClose"
        custom-class="remote-control-dialog">

        <!-- 输入控制码状态 -->
        <div v-if="status === 'input'" class="input-container">
            <div class="input-section">
                <h3>请输入控制码</h3>
                <el-input
                    v-model="inputControlCode"
                    placeholder="请输入6位数字控制码"
                    maxlength="6"
                    show-word-limit
                    size="large"
                    class="control-code-input"
                    @keyup.enter="connectToTarget">
                    <template slot="prepend">控制码</template>
                </el-input>
                <p class="input-tip">请向被控制方获取6位数字控制码</p>
            </div>
        </div>

        <!-- 连接中状态 -->
        <div v-else-if="status === 'connecting'" class="status-container">
            <i class="el-icon-loading loading-icon"></i>
            <p>正在连接到目标设备...</p>
        </div>

        <!-- 已连接状态 -->
        <div v-else-if="status === 'connected'" class="connected-container">
            <div class="status-info">
                <i class="el-icon-success status-icon connected"></i>
                <span>远程控制已建立</span>
            </div>

            <div class="target-info">
                <p>目标用户：{{ controlledUserId }}</p>
                <p>连接时间：{{ connectTime }}</p>
                <p>控制码：{{ currentControlCode }}</p>
            </div>

            <div class="control-panel">
                <h4>控制面板</h4>
                <div class="control-buttons">
                    <el-button
                        type="primary"
                        icon="el-icon-zoom-in"
                        @click="sendControlCommand('ZOOM_IN')"
                        size="small">
                        放大地图
                    </el-button>
                    <el-button
                        type="primary"
                        icon="el-icon-zoom-out"
                        @click="sendControlCommand('ZOOM_OUT')"
                        size="small">
                        缩小地图
                    </el-button>
                    <el-button
                        type="primary"
                        icon="el-icon-refresh"
                        @click="sendControlCommand('RESET_VIEW')"
                        size="small">
                        复位视角
                    </el-button>
                    <el-button
                        type="primary"
                        icon="el-icon-location"
                        @click="sendControlCommand('RESET_NORTH')"
                        size="small">
                        指北针
                    </el-button>
                </div>
            </div>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="status === 'error'" class="error-container">
            <i class="el-icon-error status-icon error"></i>
            <p>{{ errorMessage }}</p>
        </div>

        <div slot="footer" class="dialog-footer">
            <el-button
                v-if="status === 'input'"
                type="primary"
                @click="connectToTarget"
                :disabled="!inputControlCode || inputControlCode.length !== 6">
                连接
            </el-button>
            <el-button
                v-if="status === 'connected'"
                type="danger"
                @click="disconnectControl">
                断开控制
            </el-button>
            <el-button @click="handleClose">关闭</el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    name: 'RemoteControl',
    data() {
        return {
            visible: false,
            status: 'input', // input, connecting, connected, error
            inputControlCode: '',
            currentControlCode: '',
            controlledUserId: '',
            connectTime: '',
            errorMessage: '',
            websocket: null,
        }
    },
    methods: {
        show() {
            this.visible = true
            this.status = 'input'
            this.inputControlCode = ''
            this.currentControlCode = ''
            this.controlledUserId = ''
            this.connectTime = ''
            this.errorMessage = ''
        },

        hide() {
            this.visible = false
            this.disconnectControl()
        },

        handleClose() {
            this.hide()
            this.$emit('close')
        },

        async connectToTarget() {
            if (!this.inputControlCode || this.inputControlCode.length !== 6) {
                this.$message.warning('请输入6位数字控制码')
                return
            }

            this.status = 'connecting'

            try {
                const userId = this.$store.getters['auth/user']?.id || 'controller_' + Date.now()

                const response = await fetch('http://localhost:8093/api/remote-control/connect', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        controlCode: this.inputControlCode,
                        userId: userId
                    })
                })

                const result = await response.json()

                if (result.success) {
                    this.currentControlCode = result.data.controlCode
                    this.controlledUserId = result.data.controlledUserId
                    this.connectTime = new Date().toLocaleString()
                    this.status = 'connected'
                    this.$message.success('远程控制连接成功')
                    console.log('远程控制连接成功:', result.data)
                } else {
                    this.status = 'error'
                    this.errorMessage = result.message
                    this.$message.error(result.message)
                }
            } catch (error) {
                console.error('连接失败:', error)
                this.status = 'error'
                this.errorMessage = '连接服务器失败'
                this.$message.error('连接服务器失败')
            }
        },

        async disconnectControl() {
            try {
                if (this.currentControlCode) {
                    await fetch('http://localhost:8093/api/remote-control/disconnect', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            controlCode: this.currentControlCode
                        })
                    })
                }
            } catch (error) {
                console.error('断开连接失败:', error)
            }

            this.status = 'input'
            this.currentControlCode = ''
            this.controlledUserId = ''
            this.connectTime = ''
            this.$message.info('远程控制已断开')
        },

        async sendControlCommand(command) {
            if (this.status === 'connected' && this.currentControlCode) {
                try {
                    const response = await fetch('http://localhost:8093/api/remote-control/command', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            controlCode: this.currentControlCode,
                            command: command
                        })
                    })

                    const result = await response.json()

                    if (result.success) {
                        this.$message.success(`已发送${this.getCommandName(command)}命令`)
                        console.log('控制命令发送成功:', command)
                    } else {
                        this.$message.error('发送命令失败: ' + result.message)
                    }
                } catch (error) {
                    console.error('发送控制命令失败:', error)
                    this.$message.error('发送命令失败')
                }
            }
        },

        getCommandName(command) {
            const commandNames = {
                'ZOOM_IN': '放大地图',
                'ZOOM_OUT': '缩小地图',
                'RESET_VIEW': '复位视角',
                'RESET_NORTH': '指北针'
            }
            return commandNames[command] || command
        },


    },

    beforeDestroy() {
        // 清理资源
        this.disconnectControl()
    }
}
</script>

<style lang="scss" scoped>
.remote-control-dialog {
    .input-container {
        .input-section {
            text-align: center;

            h3 {
                margin-bottom: 20px;
                color: #303133;
            }

            .control-code-input {
                margin-bottom: 15px;

                ::v-deep .el-input__inner {
                    text-align: center;
                    font-size: 18px;
                    font-weight: bold;
                    letter-spacing: 2px;
                }
            }

            .input-tip {
                color: #909399;
                font-size: 12px;
            }
        }
    }

    .status-container {
        text-align: center;
        padding: 20px;

        .loading-icon {
            font-size: 24px;
            color: #409EFF;
            margin-bottom: 10px;
        }
    }

    .connected-container {
        .status-info {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;

            .status-icon.connected {
                color: #67C23A;
                margin-right: 8px;
                font-size: 16px;
            }
        }

        .target-info {
            background: #f0f9ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;

            p {
                margin: 5px 0;
                color: #606266;
            }
        }

        .control-panel {
            h4 {
                margin-bottom: 15px;
                color: #303133;
            }

            .control-buttons {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 10px;

                .el-button {
                    width: 100%;
                }
            }
        }
    }

    .error-container {
        text-align: center;
        padding: 20px;

        .status-icon.error {
            font-size: 24px;
            color: #F56C6C;
            margin-bottom: 10px;
        }

        p {
            color: #F56C6C;
        }
    }
}
</style>
